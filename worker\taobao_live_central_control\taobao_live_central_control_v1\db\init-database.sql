


CREATE TABLE anchors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,
    anchor_id TEXT UNIQUE NOT NULL,
    anchor_cookie TEXT,
    sort INTEGER DEFAULT 0,
    password TEXT,
    status TEXT DEFAULT 'active',
    total_orders INTEGER DEFAULT 0,
    total_amount REAL DEFAULT 0,
    created_at TEXT DEFAULT (datetime('now', '+8 hours')),
    updated_at TEXT DEFAULT (datetime('now', '+8 hours'))
);
CREATE INDEX idx_anchors_anchor_id ON anchors(anchor_id);
CREATE INDEX idx_anchors_created_at ON anchors(created_at);
CREATE INDEX idx_anchors_status ON anchors(status);

-- 添加触发器以自动更新 updated_at 字段
CREATE TRIGGER anchors_updated_at 
AFTER UPDATE ON anchors
FOR EACH ROW 
BEGIN
    UPDATE anchors SET updated_at = datetime('now', '+8 hours') WHERE id = NEW.id;
END;

CREATE TABLE live_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    live_id TEXT UNIQUE NOT NULL,
    anchor_name TEXT NOT NULL,
    live_title TEXT NOT NULL,
    live_category TEXT,
    live_date TEXT NOT NULL,
    scheduled_time TEXT,
    start_time TEXT,
    live_status TEXT,
    valid_status  TEXT,
    stream_url TEXT,
    product_count INTEGER DEFAULT 0,
    average_amount DECIMAL(10,2) DEFAULT 0,
    average_commission DECIMAL(10,2) DEFAULT 0,
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_commission DECIMAL(10,2) DEFAULT 0,
    created_at DATETIME DEFAULT (datetime('now', '+8 hours')),
    updated_at DATETIME DEFAULT (datetime('now', '+8 hours'))
);

CREATE INDEX idx_live_plans_live_id ON live_plans(live_id);
CREATE INDEX idx_live_plans_anchor_name ON live_plans(anchor_name);

-- 添加触发器以自动更新 updated_at 字段
CREATE TRIGGER live_plans_updated_at 
AFTER UPDATE ON live_plans
FOR EACH ROW 
BEGIN
    UPDATE live_plans SET updated_at = datetime('now', '+8 hours') WHERE id = NEW.id;
END;

CREATE TABLE live_products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    live_id TEXT NOT NULL,
    anchor_name TEXT NOT NULL,
    product_sequence INTEGER NOT NULL,
    product_id TEXT NOT NULL,
    product_name TEXT NOT NULL,
    product_subtitle TEXT,
    product_image TEXT,
    amount DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2),
    commission_amount DECIMAL(10,2),
    product_type TEXT,
    product_category TEXT,
    spot_duration INTEGER,
    explanation_status TEXT ,
    script_content TEXT,
    audio_extraction_status TEXT DEFAULT '未生成',
    push_status TEXT DEFAULT '未推送',
    created_at DATETIME DEFAULT (datetime('now', '+8 hours')),
    updated_at DATETIME DEFAULT (datetime('now', '+8 hours'))
);

CREATE INDEX idx_live_products_product_id ON live_products(product_id);
CREATE INDEX idx_live_products_live_id ON live_products(live_id);
CREATE INDEX idx_live_products_anchor_name ON live_products(anchor_name);
CREATE UNIQUE INDEX idx_live_products_unique ON live_products(live_id, product_id);

-- 添加触发器以自动更新 updated_at 字段
CREATE TRIGGER live_products_updated_at 
AFTER UPDATE ON live_products
FOR EACH ROW 
BEGIN
    UPDATE live_products SET updated_at = datetime('now', '+8 hours') WHERE id = NEW.id;
END;

-- 异步任务表
CREATE TABLE async_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_name TEXT NOT NULL,                   
    task_type TEXT NOT NULL,                  
    anchor_name TEXT NOT NULL,                
    live_id TEXT,                             
    task_params TEXT,                          
    priority INTEGER DEFAULT 5,               

  
    task_count INTEGER NOT NULL DEFAULT 0,    
    success_count INTEGER DEFAULT 0,           
    failed_count INTEGER DEFAULT 0,           
    current_product_id TEXT,                  
    current_sequence INTEGER DEFAULT 0,        
    progress_percentage DECIMAL(5,2) DEFAULT 0, 

    
    status TEXT DEFAULT 'pending',    
    current_event TEXT,     
         
    error_message TEXT,                       
    retry_count INTEGER DEFAULT 0,           
    max_retries INTEGER DEFAULT 3,          

    
    created_at DATETIME DEFAULT (datetime('now', '+8 hours')),
    started_at DATETIME,                      
    completed_at DATETIME,    
    paused_at  DATETIME,           
    updated_at DATETIME DEFAULT (datetime('now', '+8 hours'))
);

CREATE INDEX idx_async_tasks_anchor ON async_tasks(anchor_name);
CREATE INDEX idx_async_tasks_live_id ON async_tasks(live_id);
CREATE INDEX idx_async_tasks_priority ON async_tasks(priority DESC);

-- 添加触发器以自动更新 updated_at 字段
CREATE TRIGGER async_tasks_updated_at 
AFTER UPDATE ON async_tasks
FOR EACH ROW 
BEGIN
    UPDATE async_tasks SET updated_at = datetime('now', '+8 hours') WHERE id = NEW.id;
END;

-- 任务详情表（存储每个子任务的执行详情）
CREATE TABLE task_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,    
    live_id TEXT,                   
    product_id TEXT,                         
    product_name TEXT,                     
    sequence_number INTEGER,                   
    status TEXT DEFAULT 'pending',          
    error_message TEXT,                        
    result_data TEXT,                       
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME DEFAULT (datetime('now', '+8 hours')),

    FOREIGN KEY (task_id) REFERENCES async_tasks(id) ON DELETE CASCADE
);

CREATE INDEX idx_task_details_task_id ON task_details(task_id);
CREATE INDEX idx_task_details_status ON task_details(status);
CREATE INDEX idx_task_details_product_id ON task_details(product_id);

-- GPU账号管理表
CREATE TABLE gpu_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account TEXT NOT NULL UNIQUE,          
    password TEXT NOT NULL,                
    token TEXT,       
    api_key TEXT,             
    image_id TEXT,       
    ding_key TEXT,
    status TEXT DEFAULT 'active',          
    description TEXT,                      
    last_used_at DATETIME,                
    created_at DATETIME DEFAULT (datetime('now', '+8 hours')),
    updated_at DATETIME DEFAULT (datetime('now', '+8 hours'))
);

CREATE INDEX idx_gpu_accounts_account ON gpu_accounts(account);
CREATE INDEX idx_gpu_accounts_status ON gpu_accounts(status);
CREATE INDEX idx_gpu_accounts_created_at ON gpu_accounts(created_at);

-- 添加触发器以自动更新 updated_at 字段
CREATE TRIGGER gpu_accounts_updated_at 
AFTER UPDATE ON gpu_accounts
FOR EACH ROW 
BEGIN
    UPDATE gpu_accounts SET updated_at = datetime('now', '+8 hours') WHERE id = NEW.id;
END;

