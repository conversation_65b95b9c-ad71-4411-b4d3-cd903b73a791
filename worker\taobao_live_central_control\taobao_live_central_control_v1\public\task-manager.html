<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理中心</title>

    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        /* 顶部导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        /* 主容器 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        /* 统计卡片区域 */
        .stats-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            color: #2d3748;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        }
        
        .stat-card:hover::before {
            transform: translateX(0);
        }
        
        .stat-card.total { --accent-color: #667eea; }
        .stat-card.pending { --accent-color: #f6ad55; }
        .stat-card.running { --accent-color: #4299e1; }
        .stat-card.completed { --accent-color: #48bb78; }
        .stat-card.failed { --accent-color: #f56565; }
        .stat-card.paused { --accent-color: #fd7e14; }
        
        .stat-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .stat-card.total .stat-icon { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .stat-card.pending .stat-icon { background: rgba(246, 173, 85, 0.1); color: #f6ad55; }
        .stat-card.running .stat-icon { background: rgba(66, 153, 225, 0.1); color: #4299e1; }
        .stat-card.completed .stat-icon { background: rgba(72, 187, 120, 0.1); color: #48bb78; }
        .stat-card.failed .stat-icon { background: rgba(245, 101, 101, 0.1); color: #f56565; }
        .stat-card.paused .stat-icon { background: rgba(253, 126, 20, 0.1); color: #fd7e14; }
        
        .stat-number {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            color: #718096;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        /* 筛选区域 */
        .filter-section {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            margin-bottom: 2rem;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .filter-row {
            margin-bottom: 1.5rem;
        }

        .filter-row:last-child {
            margin-bottom: 0;
        }

        .filter-label {
            font-size: 0.9rem;
            color: #4a5568;
            margin-bottom: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            background: white;
            color: #4a5568;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .filter-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        /* 全部按钮的特殊样式 */
        .filter-btn[data-status="all"].active,
        .filter-btn[data-type="all"].active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            border-color: #48bb78;
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
        }

        .filter-btn[data-status="all"]:hover,
        .filter-btn[data-type="all"]:hover {
            border-color: #48bb78;
            color: #48bb78;
        }

        .filter-input-row {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .filter-input {
            flex: 1;
            padding: 0.6rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* 下拉框特殊样式 */
        .filter-input select,
        select.filter-input {
            cursor: pointer;
            background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 0.8rem;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            padding-right: 2.5rem;
        }

        .filter-input option {
            padding: 0.5rem;
            background: white;
            color: #4a5568;
        }

        .filter-search-btn {
            padding: 0.6rem 1.2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .filter-search-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .filter-clear-btn {
            padding: 0.6rem 1.2rem;
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .filter-clear-btn:hover {
            background: #edf2f7;
            transform: translateY(-1px);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #edf2f7;
        }
        
        .btn-small {
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f6ad55 0%, #d69e2e 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }
        
        /* 任务列表区域 */
        .tasks-section {
            margin-bottom: 2rem;
        }
        
        .tasks-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1.5rem;
        }
        
        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1.2rem;
        }
        
        .task-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }



        .task-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .task-card.completed { --task-color: #48bb78; }
        .task-card.pending { --task-color: #f6ad55; }
        .task-card.running { --task-color: #4299e1; }
        .task-card.failed { --task-color: #f56565; }
        .task-card.cancelled { --task-color: #718096; }
        .task-card.paused { --task-color: #fd7e14; }

        /* 新的顶部标题栏设计 */
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(0,0,0,0.06);
        }

        .task-title-section {
            flex: 1;
            min-width: 0;
        }

        .task-title-line {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .task-id {
            font-size: 0.75rem;
            color: #718096;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .task-anchor-name {
            font-size: 0.95rem;
            font-weight: 700;
            padding: 0.2rem 0.5rem;
            border-radius: 6px;
            background-color: rgba(102, 126, 234, 0.08);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.2);
            display: inline-block;
            margin: 0 0.3rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .task-anchor-name:hover {
            background-color: rgba(102, 126, 234, 0.15);
            border-color: rgba(102, 126, 234, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .task-live-id {
            font-size: 0.75rem;
            color: #718096;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .task-status {
            display: inline-flex;
            align-items: center;
            padding: 0.3rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            white-space: nowrap;
        }
        
        .task-status.completed {
            background: rgba(72, 187, 120, 0.1);
            color: #38a169;
            border: 1px solid rgba(72, 187, 120, 0.2);
        }
        
        .task-status.pending {
            background: rgba(246, 173, 85, 0.1);
            color: #d69e2e;
            border: 1px solid rgba(246, 173, 85, 0.2);
        }
        
        .task-status.running {
            background: rgba(66, 153, 225, 0.1);
            color: #3182ce;
            border: 1px solid rgba(66, 153, 225, 0.2);
        }
        
        .task-status.failed {
            background: rgba(245, 101, 101, 0.1);
            color: #e53e3e;
            border: 1px solid rgba(245, 101, 101, 0.2);
        }
        
        .task-status.cancelled {
            background: rgba(113, 128, 150, 0.1);
            color: #4a5568;
            border: 1px solid rgba(113, 128, 150, 0.2);
        }

        .task-status.paused {
            background: rgba(253, 126, 20, 0.1);
            color: #c05621;
            border: 1px solid rgba(253, 126, 20, 0.2);
        }
        
        /* 任务类型区域 */
        .task-type-section {
            margin-bottom: 0.75rem;
        }

        .task-type-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.3rem 0.6rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
            gap: 0.3rem;
            border: 1px solid;
        }

        /* 不同任务类型的颜色 */
        .task-type-badge.hand-card {
            background: rgba(72, 187, 120, 0.1);
            color: #38a169;
            border-color: rgba(72, 187, 120, 0.2);
        }

        .task-type-badge.audio {
            background: rgba(246, 173, 85, 0.1);
            color: #d69e2e;
            border-color: rgba(246, 173, 85, 0.2);
        }

        .task-type-badge.live {
            background: rgba(66, 153, 225, 0.1);
            color: #3182ce;
            border-color: rgba(66, 153, 225, 0.2);
        }

        /* 默认颜色（备用） */
        .task-type-badge:not(.hand-card):not(.audio):not(.live) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border-color: rgba(102, 126, 234, 0.2);
        }

        /* 进度条区域 */
        .task-progress-section {
            margin-bottom: 0.75rem;
        }

        .task-progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.4rem;
        }

        .task-progress-label {
            font-size: 0.75rem;
            color: #4a5568;
            font-weight: 500;
        }

        .task-progress-text {
            font-size: 0.75rem;
            color: #718096;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        /* 当前序号和产品ID区域 */
        .task-current-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background: rgba(0,0,0,0.02);
            border-radius: 6px;
            font-size: 0.75rem;
        }

        .task-current-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .task-current-label {
            color: #718096;
            font-weight: 500;
            margin-bottom: 0.2rem;
        }

        .task-current-value {
            color: #2d3748;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        /* 时间信息区域 */
        .task-times {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            margin-bottom: 0.75rem;
            padding: 0.6rem;
            background: rgba(0,0,0,0.02);
            border-radius: 6px;
        }

        .task-time {
            flex: 1;
            text-align: center;
        }

        .task-time-label {
            font-size: 0.65rem;
            color: #718096;
            margin-bottom: 0.2rem;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        .task-time-value {
            font-size: 0.7rem;
            color: #2d3748;
            font-weight: 500;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        .task-progress {
            height: 6px;
            background: rgba(0,0,0,0.08);
            border-radius: 3px;
            overflow: hidden;
            position: relative;
        }

        .task-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--task-color, #667eea), rgba(102, 126, 234, 0.8));
            border-radius: 3px;
            transition: width 0.5s ease;
            position: relative;
        }

        .task-progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .task-actions {
            display: flex;
            gap: 0.4rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* 错误信息样式 */
        .task-error {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: rgba(245, 101, 101, 0.1);
            border: 1px solid rgba(245, 101, 101, 0.2);
            border-radius: 6px;
        }

        .task-error-label {
            font-size: 0.65rem;
            color: #e53e3e;
            font-weight: 600;
            margin-bottom: 0.2rem;
            text-transform: uppercase;
        }

        .task-error-message {
            font-size: 0.7rem;
            color: #e53e3e;
            line-height: 1.3;
        }

        /* 当前事件样式 */
        .task-current-event {
            margin-top: 0.5rem;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background: rgba(66, 153, 225, 0.1);
            border: 1px solid rgba(66, 153, 225, 0.2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .task-current-event-label {
            font-size: 0.65rem;
            color: #4299e1;
            font-weight: 600;
            text-transform: uppercase;
            min-width: 60px;
        }

        .task-current-event-value {
            font-size: 0.7rem;
            color: #4299e1;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .task-current-event-value .fa-spinner {
            color: #4299e1;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 任务卡片阴影效果 */
        .task-card.running {
            box-shadow: 0 2px 8px rgba(66, 153, 225, 0.15);
        }

        .task-card.completed {
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.15);
        }

        .task-card.failed {
            box-shadow: 0 2px 8px rgba(245, 101, 101, 0.15);
        }

        .task-card.pending {
            box-shadow: 0 2px 8px rgba(246, 173, 85, 0.15);
        }

        .task-card.paused {
            box-shadow: 0 2px 8px rgba(253, 126, 20, 0.15);
        }

        /* 进度条动画效果 */
        .task-card.running .task-progress-bar::after {
            animation: shimmer 2s infinite;
        }

        .task-card:not(.running) .task-progress-bar::after {
            display: none;
        }
        
        /* 任务卡片更新动画效果 */
        .task-updated {
            animation: taskUpdateFlash 1s ease-out;
        }
        
        @keyframes taskUpdateFlash {
            0% { 
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
                transform: scale(1);
            }
            50% { 
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
                transform: scale(1.02);
            }
            100% { 
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
                transform: scale(1);
            }
        }
        
        /* 当前执行任务卡片 */
        .current-task-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
        }
        
        .current-task-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .current-task-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .current-task-item {
            display: flex;
            flex-direction: column;
        }
        
        .current-task-label {
            font-size: 0.8rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 0.25rem;
        }
        
        .current-task-value {
            font-size: 0.9rem;
            color: #4a5568;
        }
        
        /* 分页样式 */
        .pagination-container {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            margin-top: 2rem;
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .pagination-info {
            font-size: 0.9rem;
            color: #718096;
            margin-bottom: 1rem;
        }
        
        .pagination-nav {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            background: white;
            color: #4a5568;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .pagination-btn:hover:not(:disabled) {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }
        
        .pagination-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }
        
        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .filter-buttons {
                flex-wrap: wrap;
            }

            .filter-btn {
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }

            .filter-input-row {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-input {
                margin-bottom: 0.5rem;
            }

            .tasks-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .task-card {
                padding: 0.8rem;
            }

            .task-times {
                flex-direction: column;
                gap: 0.5rem;
            }

            .task-current-info {
                flex-direction: column;
                gap: 0.5rem;
            }

            .nav-links {
                display: none;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .tasks-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1025px) {
            .tasks-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1400px) {
            .tasks-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 1600px) {
            .tasks-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">任务管理中心</div>
            <nav class="nav-links">
                <a href="live-plans.html" class="nav-link">
                    <i class="fas fa-video"></i> 直播计划
                </a>
                <a href="task-manager.html" class="nav-link active">
                    <i class="fas fa-tasks"></i> 任务管理
                </a>
                <a href="gpu-accounts.html" class="nav-link">
                    <i class="fas fa-server"></i> GPU账号
                </a>
                <a href="anchors.html" class="nav-link">
                    <i class="fas fa-microphone"></i> 主播管理
                </a>
    </nav>
        </div>
    </header>

    <div class="main-container">
        <!-- 筛选区域 -->
        <section class="filter-section">
            <!-- 任务状态筛选 -->
            <div class="filter-row">
                <div class="filter-label">
                    <i class="fas fa-tasks"></i>
                    任务状态
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn" data-status="all">
                        <i class="fas fa-list"></i>
                        全部状态
                    </button>
                    <button class="filter-btn active" data-status="pending">
                        <i class="fas fa-clock"></i>
                        待执行
                    </button>
                    <button class="filter-btn active" data-status="running">
                        <i class="fas fa-spinner"></i>
                        执行中
                    </button>
                    <button class="filter-btn" data-status="completed">
                        <i class="fas fa-check-circle"></i>
                        已完成
                    </button>
                    <button class="filter-btn" data-status="failed">
                        <i class="fas fa-times-circle"></i>
                        失败
                    </button>
                    <button class="filter-btn" data-status="cancelled">
                        <i class="fas fa-ban"></i>
                        已取消
                    </button>
                    <button class="filter-btn" data-status="paused">
                        <i class="fas fa-pause"></i>
                        已暂停
                    </button>
                </div>
            </div>

            <!-- 任务类型筛选 -->
            <div class="filter-row">
                <div class="filter-label">
                    <i class="fas fa-tag"></i>
                    任务类型
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-type="all">
                        <i class="fas fa-th-large"></i>
                        全部类型
                    </button>
                    <button class="filter-btn" data-type="hand_card">
                        <i class="fas fa-hand-paper"></i>
                        手卡提取
                    </button>
                    <button class="filter-btn" data-type="audio">
                        <i class="fas fa-volume-up"></i>
                        音频生成
                    </button>
                    <button class="filter-btn" data-type="live">
                        <i class="fas fa-video"></i>
                        直播任务
                    </button>
                </div>
            </div>

            <!-- 主播名称选择 -->
            <div class="filter-row">
                <div class="filter-label">
                    <i class="fas fa-user"></i>
                    主播名称
                </div>
                <div id="anchorButtons" class="filter-buttons">
                    <button class="filter-btn active" data-anchor="">
                        <i class="fas fa-users"></i>
                        全部主播
                    </button>
                    <!-- 主播按钮将通过JavaScript动态生成 -->
                </div>
                <input type="hidden" id="anchorFilter" name="anchor" value="">
                <!-- <div class="filter-input-row" style="margin-top: 1rem;">
                    <button type="button" onclick="updateFilterDisplay(); loadTasks()" class="filter-search-btn">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <button type="button" onclick="clearFilters()" class="filter-clear-btn">
                        <i class="fas fa-redo"></i>
                        重置
                    </button>
                </div> -->
            </div>

            <!-- 当前筛选条件显示 -->
            <div class="filter-row" id="currentFiltersDisplay" style="display: none;">
                <div class="filter-label">
                    <i class="fas fa-filter"></i>
                    当前筛选条件
                </div>
                <div id="currentFiltersContent" style="font-size: 0.85rem; color: #4a5568; padding: 0.5rem; background: rgba(102, 126, 234, 0.05); border-radius: 8px;">
                    <!-- 筛选条件将通过JS动态生成 -->
                </div>
            </div>
        </section>

        <!-- 统计卡片区域 -->
        <section class="stats-section">
            <h2 class="section-title">任务概览</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- 默认占位 -->
                <div class="stat-card total">
                    <div class="stat-icon">📊</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">总任务数</div>
                        </div>
                <div class="stat-card pending">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">待执行</div>
                        </div>
                <div class="stat-card running">
                    <div class="stat-icon">🔄</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">执行中</div>
                    </div>
                <div class="stat-card completed">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card failed">
                    <div class="stat-icon">❌</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">失败</div>
                        </div>
                        </div>
        </section>

        <!-- 当前执行任务 -->
        <div id="currentTaskCard" class="current-task-card" style="display: none;">
            <h3 class="current-task-title">
                <i class="fas fa-rocket"></i>当前执行任务
            </h3>
            <div id="currentTaskInfo" class="current-task-info"></div>
        </div>



        <!-- 任务列表区域 -->
        <section class="tasks-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h3 class="tasks-title" style="margin-bottom: 0;">任务列表</h3>
                <div id="refreshIndicator" style="display: none; color: #667eea; font-size: 0.85rem;">
                    <i class="fas fa-sync fa-spin"></i> 实时更新中...
                </div>
            </div>
            <div class="tasks-grid" id="taskList">
            <!-- 任务卡片将通过JS动态生成 -->
        </div>
        </section>

        <!-- 分页 -->
        <div class="pagination-container" id="pagination" style="display: none;">
            <div class="pagination-info">
                        显示第 <span id="startItem" class="font-medium">1</span> 到第
                        <span id="endItem" class="font-medium">10</span> 条，共
                        <span id="totalItems" class="font-medium">0</span> 条
                </div>
            <div class="pagination-nav" id="paginationNav">
                        <!-- 分页按钮将通过JavaScript填充 -->
                </div>
            <!-- 移动端分页按钮（隐藏） -->
            <button id="prevPageMobile" style="display: none;">上一页</button>
            <button id="nextPageMobile" style="display: none;">下一页</button>
            </div>
        </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['layer', 'form'], function(){
            const layer = layui.layer;
            const form = layui.form;
            
            let currentPage = 1;
            const pageSize = 12;
            let totalPages = 0;
            
            // 初始化
            $(document).ready(function() {
            init();
            });
            
            function init() {
                loadStats();
                loadAnchors(); // 加载主播列表
                loadTasks();

                initFilterButtons();
                
                // 绑定"全部主播"按钮点击事件
                bindAnchorButtonEvents();

                // 设置定时刷新
                // 统计信息和当前任务状态刷新 - 每5秒
                setInterval(() => {
                    // 只有当页面可见时才刷新
                    if (!document.hidden) {
                        loadStats();
                    }
                }, 5000);

                // 任务列表刷新 - 每3秒（及时显示新任务和更新状态）
                setInterval(() => {
                    // 只有当页面可见时才刷新
                    if (!document.hidden) {
                        loadTasks(true); // 显示刷新指示器
                    }
                }, 3000);
            }

            // 加载主播列表
            async function loadAnchors() {
                try {
                    // 先尝试获取完整的主播信息（包含sort字段）
                    const response = await fetch('/api/anchors?mode=full', {
                        headers: getApiKeyHeader()
                    });
                    const result = await response.json();
                    
                    if (result.anchors && Array.isArray(result.anchors)) {
                        renderAnchorButtons(result.anchors);
                    } else {
                        // 如果获取完整信息失败，回退到简单的主播名称列表
                        const fallbackResponse = await fetch('/api/task/anchors', {
                            headers: getApiKeyHeader()
                        });
                        const fallbackResult = await fallbackResponse.json();
                        
                        if (fallbackResult.success && fallbackResult.anchors) {
                            // 将字符串数组转换为对象数组格式
                            const anchorObjects = fallbackResult.anchors.map((name, index) => ({
                                anchor_name: name,
                                sort: index // 使用索引作为排序
                            }));
                            renderAnchorButtons(anchorObjects);
                        }
                    }
                } catch (error) {
                    console.error('加载主播列表失败:', error);
                }
            }

            // 渲染主播按钮
            function renderAnchorButtons(anchors) {
                // 按sort字段正序排列主播
                const sortedAnchors = [...anchors].sort((a, b) => {
                    const sortA = parseInt(a.sort) || 0;
                    const sortB = parseInt(b.sort) || 0;
                    return sortA - sortB;
                });

                // 生成主播按钮
                const anchorButtonsContainer = $('#anchorButtons');
                
                // 清空容器，但保留"全部主播"按钮
                anchorButtonsContainer.find('.filter-btn:not([data-anchor=""])').remove();

                // 添加主播按钮
                sortedAnchors.forEach(anchor => {
                    const button = $(`
                        <button type="button" class="filter-btn" data-anchor="${anchor.anchor_name}">
                            <i class="fas fa-user"></i>
                            ${anchor.anchor_name}
                        </button>
                    `);
                    anchorButtonsContainer.append(button);
                });

                // 绑定按钮点击事件
                bindAnchorButtonEvents();
            }

            // 绑定主播按钮点击事件
            function bindAnchorButtonEvents() {
                $('.filter-btn[data-anchor]').off('click').on('click', function() {
                    const anchorName = $(this).data('anchor');
                    
                    // 更新按钮状态
                    $('.filter-btn[data-anchor]').removeClass('active');
                    $(this).addClass('active');
                    
                    // 更新隐藏的筛选字段
                    $('#anchorFilter').val(anchorName);
                    
                    // 触发查询
                    currentPage = 1;
                    updateFilterDisplay();
                    loadTasks();
                });
            }

            // 初始化筛选按钮事件
            function initFilterButtons() {
                // 状态筛选按钮 - 支持多选
                $('.filter-btn[data-status]').on('click', function() {
                    const status = $(this).data('status');
                    
                    if (status === 'all') {
                        // 点击"全部状态"按钮
                        const hasActiveSpecificStatus = $('.filter-btn[data-status]:not([data-status="all"]).active').length > 0;
                        if (hasActiveSpecificStatus) {
                            // 如果有选中的具体状态，则取消所有选中，只保留"全部"按钮选中
                            $('.filter-btn[data-status]').removeClass('active');
                            $(this).addClass('active');
                        } else {
                            // 如果没有选中具体状态，则选中所有状态（包括具体状态）
                            $('.filter-btn[data-status]').addClass('active');
                        }
                    } else {
                        // 点击具体状态按钮
                        $(this).toggleClass('active');
                        
                        // 检查是否所有具体状态都被选中
                        const allSpecificSelected = $('.filter-btn[data-status]:not([data-status="all"])').length === 
                                                  $('.filter-btn[data-status]:not([data-status="all"]).active').length;
                        
                        if (allSpecificSelected) {
                            $('.filter-btn[data-status="all"]').addClass('active');
                        } else {
                            $('.filter-btn[data-status="all"]').removeClass('active');
                        }
                    }
                    
                    currentPage = 1;
                    updateFilterDisplay();
                    loadTasks();
                });

                // 类型筛选按钮 - 支持多选
                $('.filter-btn[data-type]').on('click', function() {
                    const type = $(this).data('type');
                    
                    if (type === 'all') {
                        // 点击"全部类型"按钮
                        const hasActiveSpecificType = $('.filter-btn[data-type]:not([data-type="all"]).active').length > 0;
                        if (hasActiveSpecificType) {
                            // 如果有选中的具体类型，则取消所有选中，只保留"全部"按钮选中
                            $('.filter-btn[data-type]').removeClass('active');
                            $(this).addClass('active');
                        } else {
                            // 如果没有选中具体类型，则选中所有类型（包括具体类型）
                            $('.filter-btn[data-type]').addClass('active');
                        }
                    } else {
                        // 点击具体类型按钮
                        $(this).toggleClass('active');
                        
                        // 检查是否所有具体类型都被选中
                        const allSpecificSelected = $('.filter-btn[data-type]:not([data-type="all"])').length === 
                                                  $('.filter-btn[data-type]:not([data-type="all"]).active').length;
                        
                        if (allSpecificSelected) {
                            $('.filter-btn[data-type="all"]').addClass('active');
                        } else {
                            $('.filter-btn[data-type="all"]').removeClass('active');
                        }
                    }
                    
                    currentPage = 1;
                    updateFilterDisplay();
                    loadTasks();
                });

                // 主播按钮点击事件（将在loadAnchors中绑定）

                // 初始化筛选条件显示
                updateFilterDisplay();
            }

            // 更新筛选条件显示
            function updateFilterDisplay() {
                const selectedStatuses = [];
                let hasAllStatus = false;
                $('.filter-btn[data-status].active').each(function() {
                    const status = $(this).data('status');
                    if (status === 'all') {
                        hasAllStatus = true;
                    } else {
                        selectedStatuses.push($(this).text().trim());
                    }
                });

                const selectedTypes = [];
                let hasAllType = false;
                $('.filter-btn[data-type].active').each(function() {
                    const type = $(this).data('type');
                    if (type === 'all') {
                        hasAllType = true;
                    } else {
                        selectedTypes.push($(this).text().trim());
                    }
                });

                const anchorName = $('#anchorFilter').val();

                let filterInfo = [];
                
                // 状态显示逻辑
                if (hasAllStatus && selectedStatuses.length === 0) {
                    // 只选中了"全部"按钮，没有选中具体状态
                    filterInfo.push(`状态: 全部`);
                } else if (selectedStatuses.length === $('.filter-btn[data-status]:not([data-status="all"])').length) {
                    // 选中了所有具体状态
                    filterInfo.push(`状态: 全部`);
                } else if (selectedStatuses.length > 0) {
                    // 选中了部分具体状态
                    filterInfo.push(`状态: ${selectedStatuses.join(', ')}`);
                } else {
                    // 没有选中任何状态
                    filterInfo.push(`状态: 无选择`);
                }
                
                // 类型显示逻辑
                if (hasAllType && selectedTypes.length === 0) {
                    // 只选中了"全部"按钮，没有选中具体类型
                    filterInfo.push(`类型: 全部`);
                } else if (selectedTypes.length === $('.filter-btn[data-type]:not([data-type="all"])').length) {
                    // 选中了所有具体类型
                    filterInfo.push(`类型: 全部`);
                } else if (selectedTypes.length > 0) {
                    // 选中了部分具体类型
                    filterInfo.push(`类型: ${selectedTypes.join(', ')}`);
                } else {
                    // 没有选中任何类型
                    filterInfo.push(`类型: 无选择`);
                }
                
                // 主播显示逻辑
                if (anchorName) {
                    filterInfo.push(`主播: ${anchorName}`);
                } else {
                    filterInfo.push(`主播: 全部主播`);
                }

                $('#currentFiltersContent').html(filterInfo.join(' | '));
                $('#currentFiltersDisplay').show();
            }
            
            // 加载统计信息
            async function loadStats() {
                try {
                    const response = await fetch('/api/task/stats', {
                        headers: getApiKeyHeader()
                    });
                    const result = await response.json();
                    
                    if (result.success) {
                        renderStats(result.stats);
                        renderCurrentTask(result.currentStatus);
                    }
                } catch (error) {
                    console.error('加载统计信息失败:', error);
                }
            }
            
            // 渲染统计信息
            function renderStats(stats) {
                // 统计数据处理
                const summary = {
                    total: 0,
                    pending: 0,
                    running: 0,
                    completed: 0,
                    failed: 0,
                    paused: 0
                };
                
                stats.forEach(stat => {
                    summary.total += stat.count;
                    summary[stat.status] = (summary[stat.status] || 0) + stat.count;
                });
                
                $('#statsGrid').html(`
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-number">${summary.total}</div>
                        <div class="stat-label">总任务数</div>
                            </div>
                    <div class="stat-card pending">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-number">${summary.pending}</div>
                        <div class="stat-label">待执行</div>
                            </div>
                    <div class="stat-card running">
                        <div class="stat-icon">🔄</div>
                        <div class="stat-number">${summary.running}</div>
                        <div class="stat-label">执行中</div>
                        </div>
                    <div class="stat-card completed">
                        <div class="stat-icon">✅</div>
                        <div class="stat-number">${summary.completed}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-card failed">
                        <div class="stat-icon">❌</div>
                        <div class="stat-number">${summary.failed}</div>
                        <div class="stat-label">失败</div>
                    </div>
                    <div class="stat-card paused">
                        <div class="stat-icon">⏸️</div>
                        <div class="stat-number">${summary.paused}</div>
                        <div class="stat-label">暂停</div>
                    </div>
                `);
            }
            
            // 渲染当前执行任务
            function renderCurrentTask(currentStatus) {
                if (currentStatus.isProcessing && currentStatus.processingTasks && currentStatus.processingTasks.length > 0) {
                    $('#currentTaskCard').show();
                    
                    let tasksHtml = '';
                    
                    // 添加任务总数统计
                    tasksHtml += `
                        <div class="current-task-item" style="grid-column: 1 / -1; background: rgba(102, 126, 234, 0.1); padding: 0.75rem; border-radius: 8px; margin-bottom: 1rem;">
                            <div class="current-task-label" style="color: #667eea; font-weight: 600;">
                                <i class="fas fa-tasks"></i> 正在执行
                            </div>
                            <div class="current-task-value" style="color: #667eea; font-weight: 700;">
                                ${currentStatus.processingTaskCount} 个任务并发执行中
                            </div>
                        </div>
                    `;
                    
                    // 显示每个正在执行的任务
                    currentStatus.processingTasks.forEach((task, index) => {
                        const taskTypeIcon = getTaskTypeIcon(task.type);
                        const taskTypeText = getTaskTypeText(task.type);
                        
                        tasksHtml += `
                            <div class="current-task-item" style="padding: 0.6rem; background: rgba(0,0,0,0.02); border-radius: 6px; border-left: 3px solid ${getTaskTypeColor(task.type)};">
                                <div class="current-task-label" style="font-size: 0.8rem;">
                                    ${taskTypeIcon} ${taskTypeText} - ${task.anchorName}
                                </div>
                                <div class="current-task-value" style="font-size: 0.85rem;">
                                    ${task.name} <span style="color: #718096; font-size: 0.75rem;">(ID: ${task.id})</span>
                                </div>
                            </div>
                        `;
                    });
                    
                    $('#currentTaskInfo').html(tasksHtml);
                } else {
                    $('#currentTaskCard').hide();
                }
            }
            
            // 工具函数：获取任务类型图标
            function getTaskTypeIcon(type) {
                const iconMap = {
                    'hand_card': '<i class="fas fa-hand-paper" style="color: #48bb78;"></i>',
                    'audio': '<i class="fas fa-volume-up" style="color: #f6ad55;"></i>',
                    'live': '<i class="fas fa-video" style="color: #4299e1;"></i>'
                };
                return iconMap[type] || '<i class="fas fa-cog"></i>';
            }
            
            // 工具函数：获取任务类型颜色
            function getTaskTypeColor(type) {
                const colorMap = {
                    'hand_card': '#48bb78',
                    'audio': '#f6ad55',
                    'live': '#4299e1'
                };
                return colorMap[type] || '#667eea';
            }
            

            
            // 加载任务列表
            async function loadTasks(showIndicator = false) {
                try {
                    // 如果需要显示刷新指示器
                    if (showIndicator) {
                        $('#refreshIndicator').show();
                    }
                    const params = new URLSearchParams({
                        page: currentPage,
                        limit: pageSize
                    });

                    // 获取所有选中的状态
                    const selectedStatuses = [];
                    $('.filter-btn[data-status].active').each(function() {
                        const status = $(this).data('status');
                        if (status && status !== 'all') {
                            selectedStatuses.push(status);
                        }
                    });

                    // 获取所有选中的任务类型
                    const selectedTypes = [];
                    $('.filter-btn[data-type].active').each(function() {
                        const type = $(this).data('type');
                        if (type && type !== 'all') {
                            selectedTypes.push(type);
                        }
                    });

                    const anchorName = $('#anchorFilter').val();

                    // 发送多个独立参数 - 让后端接收数组处理OR条件
                    // 只有当实际选中了具体状态时才发送参数
                    if (selectedStatuses.length > 0) {
                        selectedStatuses.forEach(status => {
                            params.append('status', status);
                        });
                    }

                    // 只有当实际选中了具体类型时才发送参数
                    if (selectedTypes.length > 0) {
                        selectedTypes.forEach(type => {
                            params.append('taskType', type);
                        });
                    }

                    if (anchorName) params.append('anchorName', anchorName);

                    const response = await fetch(`/api/task/list?${params}`, {
                        headers: getApiKeyHeader()
                    });
                    const result = await response.json();

                    if (result.success) {
                        renderTasks(result.tasks);
                        renderPagination(result.pagination);
                    } else {
                        layer.msg('加载任务列表失败: ' + result.message, {icon: 2});
                    }
                } catch (error) {
                    console.error('加载任务列表失败:', error);
                    layer.msg('加载任务列表失败', {icon: 2});
                } finally {
                    // 隐藏刷新指示器
                    if (showIndicator) {
                        setTimeout(() => {
                            $('#refreshIndicator').hide();
                        }, 500);
                    }
                }
            }

            // 将函数暴露到全局作用域
            window.loadTasks = loadTasks;
            
            // 渲染任务列表
            function renderTasks(tasks) {

                if (tasks.length === 0) {
                    $('#taskList').html(`
                        <div class="task-card">
                            <div style="text-align: center; padding: 2rem;">
                                <i class="fas fa-tasks" style="font-size: 3rem; color: #cbd5e0; margin-bottom: 1rem;"></i>
                                <p style="color: #718096;">暂无任务数据</p>
                            </div>
                        </div>
                    `);
                    return;
                }

                $('#taskList').html(tasks.map(task => `
                    <div class="task-card ${task.status}">
                        <!-- 顶部标题栏：#id - 主播名称 - 直播id，右上角状态 -->
                        <div class="task-header">
                            <div class="task-title-section">
                                <div class="task-title-line">
                                    <span class="task-id">#${task.id}</span>
                                    <span class="task-anchor-name">${task.anchor_name}</span>
                                    <span class="task-live-id">${task.live_id || 'N/A'}</span>
                                </div>
                            </div>
                            <div class="task-status ${task.status}">${getStatusText(task.status)}</div>
                        </div>

                        <!-- 任务类型 -->
                        <div class="task-type-section">
                            <div class="task-type-badge ${getTaskTypeClass(task.task_type)}">
                                <i class="fas fa-tag"></i>
                                ${getTaskTypeText(task.task_type)}
                            </div>
                        </div>

                        <!-- 进度条和进度情况 -->
                        <div class="task-progress-section">
                            <div class="task-progress-header">
                                <span class="task-progress-label">执行进度</span>
                                <span class="task-progress-text">${task.success_count}/${task.task_count} (${task.progress_percentage}%)</span>
                            </div>
                            <div class="task-progress">
                                <div class="task-progress-bar" style="width: ${task.progress_percentage}%"></div>
                            </div>
                            ${task.failed_count > 0 ? `
                                <div style="font-size: 0.7rem; color: #e53e3e; margin-top: 0.3rem;">
                                    <i class="fas fa-exclamation-triangle"></i> 失败 ${task.failed_count} 个
                                </div>
                            ` : ''}
                        </div>

                        <!-- 当前事件状态 -->
                        ${task.current_event ? `
                            <div class="task-current-event">
                                <div class="task-current-event-label">当前状态</div>
                                <div class="task-current-event-value">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>${task.current_event}
                                </div>
                            </div>
                        ` : ''}

                        <!-- 当前序号，当前产品id -->
                        ${task.current_product_id ? `
                            <div class="task-current-info">
                                <div class="task-current-item">
                                    <div class="task-current-label">当前序号</div>
                                    <div class="task-current-value">${task.current_sequence || 'N/A'}</div>
                                </div>
                                <div class="task-current-item">
                                    <div class="task-current-label">当前产品ID</div>
                                    <div class="task-current-value">${task.current_product_id}</div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- 创建时间，开始时间 -->
                        <div class="task-times">
                             <div class="task-time">
                                <div class="task-time-label">开始时间</div>
                                <div class="task-time-value">${task.started_at ? formatDateTime(task.started_at) : '未开始'}</div>
                            </div>
                            <div class="task-time">
                                <div class="task-time-label">完成时间</div>
                                <div class="task-time-value">${task.completed_at ? formatDateTime(task.completed_at) : '未完成'}</div>
                            </div>
                        </div>

                        <!-- 错误信息 -->
                        ${task.error_message ? `
                            <div class="task-error">
                                <div class="task-error-label">错误信息</div>
                                <div class="task-error-message">${task.error_message}</div>
                            </div>
                        ` : ''}

                        <!-- 操作按钮 -->
                        <div class="task-actions">
                            <button onclick="viewTaskDetails(${task.id})" class="btn btn-primary btn-small">
                                <i class="fas fa-list"></i> 详情
                            </button>
                            ${task.task_type === 'hand_card' && task.status === 'pending' ? `
                                <button onclick="startHandCardTask(${task.id})" class="btn btn-success btn-small">
                                    <i class="fas fa-play"></i> 立即执行
                                </button>
                            ` : ''}
                            ${task.status === 'failed' ? `
                                <button onclick="retryTask(${task.id})" class="btn btn-warning btn-small">
                                    <i class="fas fa-redo"></i> 重试
                                </button>
                            ` : ''}
                            ${task.status === 'running' ? `
                                <button onclick="pauseTask(${task.id})" class="btn btn-warning btn-small">
                                    <i class="fas fa-pause"></i> 暂停
                                </button>
                            ` : ''}
                            ${task.status === 'paused' ? `
                                <button onclick="resumeTask(${task.id})" class="btn btn-success btn-small">
                                    <i class="fas fa-play"></i> 恢复
                                </button>
                            ` : ''}
                            ${['pending', 'running', 'paused'].includes(task.status) ? `
                                <button onclick="cancelTask(${task.id})" class="btn btn-danger btn-small">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `).join(''));
            }
            
            // 渲染分页
            function renderPagination(pagination) {
                totalPages = pagination.totalPages || 1;
                const total = pagination.total || 0;
                const page = pagination.page || 1;
                const limit = pagination.limit || 10;
                
                if (totalPages <= 1) {
                    $('#pagination').hide();
                    return;
                }
                
                $('#pagination').show();

                // 更新分页信息
                const startItem = total === 0 ? 0 : (page - 1) * limit + 1;
                const endItem = Math.min(page * limit, total);

                $('#startItem').text(startItem);
                $('#endItem').text(endItem);
                $('#totalItems').text(total);

                // 更新移动端分页按钮
                $('#prevPageMobile').prop('disabled', page <= 1);
                $('#nextPageMobile').prop('disabled', page >= totalPages);
                
                $('#prevPageMobile').off('click').on('click', () => page > 1 && changePage(page - 1));
                $('#nextPageMobile').off('click').on('click', () => page < totalPages && changePage(page + 1));

                // 生成桌面端分页按钮
                generatePaginationButtons(page, totalPages);
            }
            
            // 生成分页按钮
            function generatePaginationButtons(currentPage, totalPages) {
                $('#paginationNav').empty();

                if (totalPages <= 1) return;

                // 上一页按钮
                const prevDisabled = currentPage <= 1;
                $('#paginationNav').append(`
                    <button onclick="${!prevDisabled ? 'changePage(' + (currentPage - 1) + ')' : ''}"
                            class="pagination-btn ${prevDisabled ? '' : ''}" ${prevDisabled ? 'disabled' : ''}>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                `);

                // 页码按钮
                const startPage = Math.max(1, currentPage - 2);
                const endPage = Math.min(totalPages, currentPage + 2);

                if (startPage > 1) {
                    $('#paginationNav').append(`
                        <button onclick="changePage(1)" class="pagination-btn">1</button>
                    `);
                    if (startPage > 2) {
                        $('#paginationNav').append(`<span class="pagination-btn" style="cursor: default;">...</span>`);
                    }
                }

                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === currentPage;
                    const buttonClass = isActive ? 'pagination-btn active' : 'pagination-btn';

                    $('#paginationNav').append(`
                        <button onclick="${!isActive ? 'changePage(' + i + ')' : ''}" class="${buttonClass}">${i}</button>
                    `);
                }

                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        $('#paginationNav').append(`<span class="pagination-btn" style="cursor: default;">...</span>`);
                    }
                    $('#paginationNav').append(`
                        <button onclick="changePage(${totalPages})" class="pagination-btn">${totalPages}</button>
                    `);
                }

                // 下一页按钮
                const nextDisabled = currentPage >= totalPages;
                $('#paginationNav').append(`
                    <button onclick="${!nextDisabled ? 'changePage(' + (currentPage + 1) + ')' : ''}"
                            class="pagination-btn" ${nextDisabled ? 'disabled' : ''}>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                `);
            }
            
            // 切换页面
            function changePage(page) {
                if (page >= 1 && page <= totalPages && page !== currentPage) {
                    currentPage = page;
                    loadTasks();
                }
            }
            






            // 清除过滤条件
            function clearFilters() {
                // 重置状态按钮 - 默认选中待执行和执行中
                $('.filter-btn[data-status]').removeClass('active');
                $('.filter-btn[data-status="pending"]').addClass('active');
                $('.filter-btn[data-status="running"]').addClass('active');

                // 重置类型按钮 - 默认只选中全部类型按钮（表示显示所有类型）
                $('.filter-btn[data-type]').removeClass('active');
                $('.filter-btn[data-type="all"]').addClass('active');

                // 重置主播按钮状态
                $('.filter-btn[data-anchor]').removeClass('active');
                $('.filter-btn[data-anchor=""]').addClass('active');
                $('#anchorFilter').val('');

                currentPage = 1;
                updateFilterDisplay();
                loadTasks();
            }

            // 函数已经在全局作用域中定义，无需再次暴露
            window.clearFilters = clearFilters;
            window.changePage = changePage;
            
            // 工具函数
            function getStatusText(status) {
                const statusMap = {
                    'pending': '待执行',
                    'running': '执行中',
                    'completed': '已完成',
                    'failed': '失败',
                    'cancelled': '已取消',
                    'paused': '已暂停'
                };
                return statusMap[status] || status;
            }
            
            function getTaskTypeText(type) {
                const typeMap = {
                    'hand_card': '手卡提取',
                    'audio': '音频生成',
                    'live': '直播任务'
                };
                return typeMap[type] || type;
            }

            function getTaskTypeClass(type) {
                const classMap = {
                    'hand_card': 'hand-card',
                    'audio': 'audio',
                    'live': 'live'
                };
                return classMap[type] || '';
            }
            
            function formatDateTime(dateStr) {
                if (!dateStr) return '';
                return new Date(dateStr).toLocaleString('zh-CN');
            }
            

            
            function getApiKeyHeader() {
                const apiKey = getCookie('api_key') || '';
                return {
                    'X-API-Key': apiKey,
                    'Content-Type': 'application/json'
                };
            }

            // 获取Cookie值
            function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            }
        });

        // 全局函数定义 - 供HTML onclick使用

        // 暂停任务
        async function pauseTask(taskId) {
            layui.use(['layer'], function(){
                const layer = layui.layer;
                layer.confirm('确定要暂停这个任务吗？', {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/task/${taskId}/pause`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        const result = await response.json();

                        if (result.success) {
                            layer.msg('任务已暂停', {icon: 1});
                            location.reload(); // 刷新页面
                        } else {
                            layer.msg('暂停失败: ' + result.message, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg('暂停失败', {icon: 2});
                    }
                    layer.close(index);
                });
            });
        }

        // 恢复任务
        async function resumeTask(taskId) {
            layui.use(['layer'], function(){
                const layer = layui.layer;
                layer.confirm('确定要恢复这个任务吗？', {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/task/${taskId}/resume`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        const result = await response.json();

                        if (result.success) {
                            layer.msg('任务已恢复', {icon: 1});
                            location.reload(); // 刷新页面
                        } else {
                            layer.msg('恢复失败: ' + result.message, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg('恢复失败', {icon: 2});
                    }
                    layer.close(index);
                });
            });
        }

        // 取消任务 - 也移到全局作用域
        async function cancelTask(taskId) {
            layui.use(['layer'], function(){
                const layer = layui.layer;
                layer.confirm('确定要取消这个任务吗？', {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/task/${taskId}/cancel`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        const result = await response.json();

                        if (result.success) {
                            layer.msg('任务已取消', {icon: 1});
                            location.reload(); // 刷新页面
                        } else {
                            layer.msg('取消失败: ' + result.message, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg('取消失败', {icon: 2});
                    }
                    layer.close(index);
                });
            });
        }

        // 重试任务 - 也移到全局作用域
        async function retryTask(taskId) {
            layui.use(['layer'], function(){
                const layer = layui.layer;
                layer.confirm('确定要重试这个任务吗？', {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/task/${taskId}/retry`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        const result = await response.json();

                        if (result.success) {
                            layer.msg('任务已重新启动', {icon: 1});
                            location.reload(); // 刷新页面
                        } else {
                            layer.msg('重试失败: ' + result.message, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg('重试失败', {icon: 2});
                    }
                    layer.close(index);
                });
            });
        }

        // 立即执行手卡任务
        async function startHandCardTask(taskId) {
            layui.use(['layer'], function(){
                const layer = layui.layer;
                layer.confirm('确定要立即执行这个手卡任务吗？', {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/task/hand-card/${taskId}/start`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        const result = await response.json();

                        if (result.success) {
                            layer.msg('手卡任务已开始执行', {icon: 1});
                            location.reload(); // 刷新页面
                        } else {
                            layer.msg('执行失败: ' + result.error, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg('执行失败', {icon: 2});
                    }
                    layer.close(index);
                });
            });
        }

        // 查看任务详情 - 也移到全局作用域
        function viewTaskDetails(taskId) {
            layui.use(['layer'], function(){
                const layer = layui.layer;
                
                // 从当前页面的任务卡片中获取任务信息
                const taskCard = $(`.task-card:has(.task-id:contains("#${taskId}"))`);
                let title = '任务详情 #' + taskId;
                
                if (taskCard.length > 0) {
                    const anchorName = taskCard.find('.task-anchor-name').text();
                    const liveId = taskCard.find('.task-live-id').text();
                    const taskType = taskCard.find('.task-type-badge').text().trim();
                    
                    // 构建完整标题
                    title = `任务详情 #${taskId} - ${anchorName} - ${liveId} - ${taskType}`;
                }
                
                // 创建弹出框显示任务详情
                layer.open({
                    type: 2, // iframe类型
                    title: title,
                    area: ['90%', '90%'], // 弹出框大小
                    maxmin: true, // 允许最大化最小化
                    content: '/task-details.html?taskId=' + taskId,
                    success: function(layero, index) {
                        // 弹出框加载成功后的回调
                        console.log('任务详情弹出框已打开');
                    },
                    end: function() {
                        // 弹出框关闭后的回调
                        console.log('任务详情弹出框已关闭');
                    }
                });
            });
        }
    </script>
</body>
</html>
