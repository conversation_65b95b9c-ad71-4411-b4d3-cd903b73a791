# 批量上品功能说明

## 功能概述

批量上品功能允许用户为指定的直播计划批量添加产品，支持一次性添加多个产品ID，并自动处理分批请求和错误处理。

## 功能特点

1. **批量添加**: 支持一次性输入多个产品ID，一行一个
2. **分批处理**: 每批最多200个产品，避免单次请求过大
3. **间隔控制**: 批次间自动间隔2秒，避免请求过于频繁
4. **实时统计**: 显示已添加、成功、失败的产品数量
5. **智能解析**: 正确解析淘宝API响应，区分成功和失败的产品
6. **自动提取**: 添加完成后自动提取最新产品数据
7. **错误处理**: 完善的错误处理和用户提示

## 使用方法

### 1. 打开批量上品弹窗

在直播计划列表的操作列中，点击"上品"按钮即可打开批量上品弹窗。

### 2. 输入产品ID

在文本框中输入产品ID，每行一个产品ID，例如：
```
724038386814
846631525226
904461720819
924279130579
```

### 3. 开始批量添加

点击"开始批量添加"按钮，系统将：
- 验证输入的产品ID格式
- 分批处理产品ID（每批200个）
- 显示实时进度和统计信息
- 自动处理请求间隔

### 4. 查看结果

添加完成后，弹窗会显示：
- 总添加数量
- 成功数量
- 失败数量

## 技术实现

### 前端实现

- **文件位置**: `public/js/batch-add-products.js`
- **主要功能**:
  - 弹窗界面管理
  - 产品ID验证
  - 调用后端API
  - 统计信息更新

### 后端实现

- **文件位置**: `src/batch-add-products.js`
- **API路由**: `/api/batch-add-products` - 批量上品接口
- **使用现有API**: `/api/live-products/sync` - 提取产品数据
- **主要功能**:
  - 主播信息验证
  - Cookie管理
  - MD5签名生成
  - 分批处理产品
  - 淘宝API调用
  - 错误处理和重试

### 请求格式

批量添加产品的请求格式参考淘宝API：

```javascript
// 请求URL
https://h5api.m.taobao.com/h5/mtop.mediaplatform.video.additem.batch/1.0/

// 请求参数
{
  liveId: "直播ID",
  batchParams: "[{\"itemId\":产品ID,\"publishParam\":{...}}]"
}
```

### 签名算法

使用MD5算法生成请求签名：
```
signString = h5Token + "&" + timestamp + "&" + appKey + "&" + dataStr
sign = MD5(signString)
```

## 注意事项

1. **产品ID格式**: 只接受纯数字格式的产品ID
2. **批次限制**: 每批最多200个产品，超出会自动分批
3. **请求间隔**: 批次间自动间隔2秒，避免被限流
4. **Cookie要求**: 需要主播配置有效的Cookie
5. **权限验证**: 需要有效的API访问密码

## 错误处理

系统会处理以下错误情况：
- 无效的产品ID格式
- 主播信息获取失败
- Cookie无效或过期
- 网络请求失败
- API响应错误

## 后续优化

1. 支持从Excel文件导入产品ID
2. 添加产品ID去重功能
3. 支持产品信息预览
4. 添加批量操作历史记录
5. 支持自定义批次大小和间隔时间
