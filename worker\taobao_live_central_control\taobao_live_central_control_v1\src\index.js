/**
 * 淘宝直播管理 Node.js Express 路由
 */

import express from 'express';
import { config } from './config.js';
import { database } from './database.js';
import LivePlanSync from './live-plan-sync.js';
import LiveProductSync from './live-product-sync.js';
import { LiveHideManager } from './live-hide-manager.js';
import BatchAddProducts from './batch-add-products.js';
import liveCardApi from './api/live-card.js';
import taskApi from './api/task.js';
import gpuAccountsApi from './api/gpu-accounts.js';
import gpuApi from './api/gpu.js';
import taskManager from './task-manager.js';
import { handleUpdateCookie } from './job.js';
import { extractAndValidateH5Token } from './utils/cookie-utils.js';
import { getAnchorByName, validateAnchorExists, validateAnchorCookie, getActiveAnchors, getAnchorByLiveId } from './utils/anchor-utils.js';
import { sendSuccessResponse, sendErrorResponse, sendBatchOperationResponse, handleApiError, validateRequiredParams } from './utils/response-utils.js';
import { triggerCookieUpdateJob } from './job.js';


// 创建路由器
const router = express.Router();

// 常量定义
const CONSTANTS = {
  AUTH: {
    SPECIAL_APIS: ['anchors']
  },
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    DEFAULT_SORT_FIELD: 'created_at',
    DEFAULT_SORT_ORDER: 'DESC'
  }
};

// 中间件：验证API密钥
async function authMiddleware(req, res, next) {
  try {
    const authResult = await validateApiKey(req);
    if (!authResult.valid) {
      return res.status(401).json({
        error: "invalid_api_key",
        message: "无效的访问密码"
      });
    }
    req.authResult = authResult;
    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    res.status(500).json({
      error: "auth_error",
      message: "认证失败"
    });
  }
}

// API路由定义

router.get("/api/anchors", authMiddleware, handleAnchorNames);
router.post("/api/anchors", authMiddleware, handleAddAnchor);
router.get("/api/anchors/list", authMiddleware, handleAnchorsData);
router.get("/api/anchors/stats", authMiddleware, handleAnchorsStats);
router.post("/api/anchors/check-password", authMiddleware, handleCheckPassword);
router.post("/api/update-cookie", authMiddleware, handleUpdateCookie);
router.post("/api/update-cookies-batch", authMiddleware, handleUpdateCookiesBatch);
router.get("/api/live-plans", authMiddleware, handleLivePlansData);
router.get("/api/live-plans/stats", authMiddleware, handleLivePlansStats);
router.post("/api/live-plans/sync", authMiddleware, handleLivePlansSync);
router.post("/api/live-plans/sync-all", authMiddleware, handleLivePlansSyncAll);
router.post("/api/live-plans/create", authMiddleware, handleLivePlansCreate);
router.post("/api/live-plans/hide-all", authMiddleware, handleLivePlansHideAll);
router.post("/api/live-plans/hide-selected", authMiddleware, handleLivePlansHideSelected);
router.get("/api/live-products/list", authMiddleware, handleLiveProductsList);
router.get("/api/live-products/stats", authMiddleware, handleLiveProductsStats);
router.post("/api/live-products/sync", authMiddleware, handleLiveProductsSync);
router.post("/api/live-products/extract-all", authMiddleware, handleLiveProductsExtractAll);
router.post("/api/live-products/extract-today", authMiddleware, handleLiveProductsExtractToday);
router.post("/api/live-products/extract-zero", authMiddleware, handleLiveProductsExtractZero);
router.post("/api/live-products/extract-recent", authMiddleware, handleLiveProductsExtractRecent);
router.post("/api/live-products/clear", authMiddleware, handleLiveProductsClear);
router.post("/api/batch-add-products", authMiddleware, handleBatchAddProducts);
router.use('/api/live-card', authMiddleware, liveCardApi);
router.use('/api/task', authMiddleware, taskApi);
router.use('/api/gpu-accounts', authMiddleware, gpuAccountsApi);
router.use('/api/gpu', authMiddleware, gpuApi);

// 动态路由

router.get("/api/anchors/:anchorId", authMiddleware, (req, res) => {
  handleGetAnchor(req.params.anchorId, req, res);
});

router.put("/api/anchors/:anchorId", authMiddleware, (req, res) => {
  handleUpdateAnchor(req.params.anchorId, req, res);
});

router.delete("/api/anchors/:anchorId", authMiddleware, (req, res) => {
  handleDeleteAnchor(req.params.anchorId, req, res);
});

/**
 * 公共工具函数
 */

// 解析请求的cookies
function parseCookies(request) {
  const cookieString = request.headers.cookie || '';
  return cookieString.split(';').reduce((cookies, cookie) => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
    return cookies;
  }, {});
}

// 创建错误响应 - Express版本
function createErrorResponse(error, message, status = 500) {
  return { error, message, status };
}



// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return "";

  try {
    const date = new Date(dateStr);
    return date.getFullYear() +
          '-' + String(date.getMonth() + 1).padStart(2, '0') +
          '-' + String(date.getDate()).padStart(2, '0') +
          ' ' + String(date.getHours()).padStart(2, '0') +
          ':' + String(date.getMinutes()).padStart(2, '0') +
          ':' + String(date.getSeconds()).padStart(2, '0');
  } catch (e) {
    return dateStr;
  }
}

// 验证访问密码 - Express版本
async function validateApiKey(request) {
  const apiKey = request.headers['x-api-key'];
  const cookies = parseCookies(request);
  const inputPassword = apiKey || cookies['api_key'];

  if (!inputPassword) {
    return {
      valid: false,
      response: createErrorResponse("invalid_api_key", "无效的访问密码", 401)
    };
  }

  // 检查是否为管理员密码
  if (inputPassword === config.auth.password) {
    return {
      valid: true,
      userType: 'admin',
      anchorInfo: null
    };
  }

  // 检查是否为主播密码
  try {
    const result = await database.all(
      "SELECT id, anchor_name, anchor_id FROM anchors WHERE password = ? AND status = 'active'",
      [inputPassword]
    );

    if (result.results?.length > 0) {
      const anchor = result.results[0];
      return {
        valid: true,
        userType: 'anchor',
        anchorInfo: {
          id: anchor.id,
          anchor_name: anchor.anchor_name,
          anchor_id: anchor.anchor_id
        }
      };
    }
  } catch (error) {
    console.error("Error checking anchor password:", error);
  }

  return {
    valid: false,
    response: createErrorResponse("invalid_api_key", "无效的访问密码", 401)
  };
}

// 公共查询和分页处理函数

// 获取分页参数 - Express版本
function getPaginationParams(req) {
  return {
    page: parseInt(req.query.page || CONSTANTS.PAGINATION.DEFAULT_PAGE),
    limit: parseInt(req.query.limit || CONSTANTS.PAGINATION.DEFAULT_LIMIT),
    sortField: req.query.sortField || CONSTANTS.PAGINATION.DEFAULT_SORT_FIELD,
    sortOrder: req.query.sortOrder || CONSTANTS.PAGINATION.DEFAULT_SORT_ORDER
  };
}

// 安全的排序处理
function getSafeSorting(sortField, sortOrder, validFields) {
  const actualSortField = validFields.includes(sortField) ? sortField : validFields[0];
  const actualSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';
  return { actualSortField, actualSortOrder };
}

// 应用主播权限过滤 - 根据表类型使用不同的字段
function applyAnchorFilter(filters, authResult, tableType = 'anchors') {
  if (authResult.userType === 'anchor' && authResult.anchorInfo) {
    // 根据不同表类型设置相应的字段
    switch (tableType) {
      case 'anchors':
        // 主播表使用 anchor_id 字段
        filters.anchorId = authResult.anchorInfo.anchor_id;
        break;
      default:
        console.warn(`Unknown table type: ${tableType}`);
    }
  }
  return filters;
}

// 构建查询条件和参数
function buildQueryConditions(filters) {
  const conditions = [];
  const params = [];

  const conditionMap = {
    anchorName: { sql: "anchor_name = ?", value: (v) => v },
    anchorId: { sql: "anchor_id = ?", value: (v) => v },
    status: { sql: "status = ?", value: (v) => v }
  };

  Object.entries(filters).forEach(([key, value]) => {
    if (value && conditionMap[key]) {
      conditions.push(conditionMap[key].sql);
      params.push(conditionMap[key].value(value));
    }
  });

  return { conditions, params };
}

// 格式化数据通用函数
function formatDataResults(results, dateFields = ['created_at', 'updated_at'], numberFields = []) {
  return results.map(item => {
    const formatted = { ...item };

    // 格式化日期字段
    dateFields.forEach(field => {
      if (formatted[field]) {
        formatted[field] = formatDate(formatted[field]);
      }
    });

    // 格式化数字字段
    numberFields.forEach(field => {
      if (formatted[field] !== undefined) {
        formatted[field] = Number(formatted[field] || 0);
      }
    });

    return formatted;
  });
}

// 密码验证公共函数
async function validatePassword(password, excludeId) {
  if (!password) {
    return {
      isValid: false,
      message: "密码不能为空"
    };
  }

  // 检查是否与系统访问密码相同
  if (password === config.auth.password) {
    return {
      isValid: false,
      message: "密码不能与系统访问密码相同，请使用其他密码"
    };
  }

  let sql = "SELECT anchor_name FROM anchors WHERE password = ?";
  let params = [password];

  if (excludeId) {
    sql += " AND id != ?";
    params.push(excludeId);
  }

  const result = await database.all(sql, params);

  if (result.results.length > 0) {
    return {
      isValid: false,
      message: `密码已被主播"${result.results[0].anchor_name}"使用，请使用其他密码`
    };
  }

  return {
    isValid: true,
    message: "密码可以使用"
  };
}

// 权限检查公共函数
function checkAnchorPermission(authResult, operation = 'modify') {
  if (authResult.userType === 'anchor') {
    const operations = {
      modify: "主播用户无权修改主播信息",
      delete: "主播用户无权删除主播信息",
      add: "主播用户无权添加新主播",
      check: "主播用户无权检查密码"
    };

    return {
      hasPermission: false,
      message: operations[operation] || "主播用户无权执行此操作"
    };
  }

  return {
    hasPermission: true,
    message: ""
  };
}











/**
 * 获取主播列表 - 支持两种模式：名称列表和完整信息 - Express版本
 */
async function handleAnchorNames(req, res) {
  try {
    const mode = req.query.mode || "names";

    if (mode === "full") {
      // 返回完整的主播信息（用于同步弹窗）
      let sql = "SELECT id, anchor_name, anchor_id, status, sort, created_at, updated_at FROM anchors";
      let params = [];
      let whereConditions = [];

      // 如果是主播用户，只返回自己的信息
      if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
        whereConditions.push("id = ?");
        params.push(req.authResult.anchorInfo.id);
      }

      // 添加WHERE子句
      if (whereConditions.length > 0) {
        sql += " WHERE " + whereConditions.join(" AND ");
      }

      sql += " ORDER BY sort ASC, anchor_name ASC";
      const result = await database.all(sql, params);

      const anchors = result.results?.map(anchor => ({
        ...anchor,
        created_at: formatDate(anchor.created_at),
        updated_at: formatDate(anchor.updated_at)
      })) || [];

      res.json({
        anchors,
        userInfo: {
          userType: req.authResult.userType,
          anchorInfo: req.authResult.anchorInfo
        }
      });
    } else {
      // 返回主播名称列表（用于筛选下拉框）
      let sql = "SELECT DISTINCT anchor_name FROM anchors WHERE anchor_name IS NOT NULL AND anchor_name != ''";
      let params = [];

      // 如果是主播用户，只返回自己的名称
      if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
        sql += " AND anchor_name = ?";
        params.push(req.authResult.anchorInfo.anchor_name);
      }

      sql += " ORDER BY anchor_name";
      const result = await database.all(sql, params);
      const anchorNames = result.results?.map(item => item.anchor_name) || [];

      res.json({
        anchorNames,
        userInfo: {
          userType: req.authResult.userType,
          anchorInfo: req.authResult.anchorInfo
        }
      });
    }
  } catch (error) {
    console.error("Error fetching anchor data:", error);
    res.status(500).json({
      error: "Failed to fetch anchor data",
      message: error.message
    });
  }
}





/**
 * 获取主播列表数据 - Express版本
 */
async function handleAnchorsData(req, res) {
  try {
    const { page, limit } = getPaginationParams(req);

    let filters = {
      anchorName: req.query.anchorName,
      anchorId: req.query.anchorId,
      status: req.query.status
    };

    filters = applyAnchorFilter(filters, req.authResult, 'anchors');
    const { conditions, params } = buildQueryConditions(filters);

    let sql = "SELECT id, anchor_name, anchor_id, status, sort, created_at, updated_at FROM anchors";
    let countSql = "SELECT COUNT(*) as total FROM anchors";

    if (conditions.length > 0) {
      const whereClause = " WHERE " + conditions.join(" AND ");
      sql += whereClause;
      countSql += whereClause;
    }

    sql += " ORDER BY sort ASC, created_at DESC LIMIT ? OFFSET ?";
    const offset = (page - 1) * limit;

    const [countResult, dataResult] = await Promise.all([
      database.all(countSql, params),
      database.all(sql, [...params, limit, offset])
    ]);

    const total = countResult.results[0]?.total || 0;
    const anchors = formatDataResults(dataResult.results);

    res.json({
      anchors,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      userInfo: {
        userType: req.authResult.userType,
        anchorInfo: req.authResult.anchorInfo
      }
    });
  } catch (error) {
    console.error("Error fetching anchors:", error);
    res.status(500).json({
      error: "Failed to fetch anchors",
      message: error.message
    });
  }
}

/**
 * 获取主播统计数据 - Express版本
 */
async function handleAnchorsStats(req, res) {
  try {
    let filters = {
      anchorName: req.query.anchorName,
      anchorId: req.query.anchorId,
      status: req.query.status
    };

    filters = applyAnchorFilter(filters, req.authResult, 'anchors');
    const { conditions, params } = buildQueryConditions(filters);
    const whereClause = conditions.length > 0 ? " WHERE " + conditions.join(" AND ") : "";

    const statsSql = `
      SELECT
        COUNT(*) as totalAnchors,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as activeAnchors
      FROM anchors
      ${whereClause}
    `;

    const statsResult = await database.all(statsSql, params);
    const stats = statsResult.results[0];

    res.json({
      totalAnchors: stats.totalAnchors || 0,
      activeAnchors: stats.activeAnchors || 0,
      userInfo: {
        userType: req.authResult.userType,
        anchorInfo: req.authResult.anchorInfo
      }
    });
  } catch (error) {
    console.error("Error fetching anchors stats:", error);
    res.status(500).json({
      error: "Failed to fetch anchors statistics",
      message: error.message
    });
  }
}

/**
 * 添加主播 - Express版本
 */
async function handleAddAnchor(req, res) {
  try {
    const permissionCheck = checkAnchorPermission(req.authResult, 'add');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    const { anchor_name, anchor_id, anchor_cookie, status, password, sort } = req.body;

    // 验证必填字段
    if (!anchor_name || !anchor_id || !password || !anchor_cookie) {
      return res.status(400).json({
        error: "missing_fields",
        message: "主播名称、主播ID、密码和主播Cookie为必填字段"
      });
    }

    // 验证密码
    const passwordValidation = await validatePassword(password, null);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: "invalid_password",
        message: passwordValidation.message
      });
    }

    // 检查主播ID是否已存在
    const checkResult = await database.all(
      "SELECT anchor_id FROM anchors WHERE anchor_id = ?",
      [anchor_id]
    );

    if (checkResult.results.length > 0) {
      return res.status(400).json({
        error: "duplicate_anchor_id",
        message: "主播ID已存在"
      });
    }

    // 插入新主播
    const query = `
      INSERT INTO anchors (anchor_name, anchor_id, anchor_cookie, status, password, sort, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, datetime('now', '+8 hours'), datetime('now', '+8 hours'))
    `;
    const values = [anchor_name, anchor_id, anchor_cookie || '', status, password, parseInt(sort) || 0];
    const insertResult = await database.run(query, values);

    res.json({
      success: true,
      message: "主播添加成功",
      id: insertResult.meta.last_row_id
    });
  } catch (error) {
    console.error("Error adding anchor:", error);
    res.status(500).json({
      error: "add_anchor_failed",
      message: "添加主播失败: " + error.message
    });
  }
}

/**
 * 获取单个主播 - Express版本
 */
async function handleGetAnchor(anchorId, req, res) {
  try {
    if (!anchorId) {
      return res.status(400).json({ error: "无效的主播ID" });
    }

    // 如果是主播用户，只能查看自己的信息
    if (req.authResult.userType === 'anchor' && req.authResult.anchorInfo) {
      if (parseInt(anchorId) !== req.authResult.anchorInfo.id) {
        return res.status(403).json({ error: "无权访问其他主播信息" });
      }
    }

    // 查询主播信息（包含密码字段）
    const result = await database.all(
      "SELECT id, anchor_name, anchor_id, anchor_cookie, status, password, sort, total_orders, total_amount, created_at, updated_at FROM anchors WHERE id = ?",
      [anchorId]
    );

    if (result.results.length === 0) {
      return res.status(404).json({ error: "主播不存在" });
    }

    const anchor = result.results[0];

    // 格式化数据
    const formattedAnchor = {
      ...anchor,
      created_at: formatDate(anchor.created_at),
      updated_at: formatDate(anchor.updated_at)
    };

    res.json({
      anchor: formattedAnchor
    });
  } catch (error) {
    console.error("Error fetching anchor:", error);
    res.status(500).json({
      error: "获取主播信息失败",
      details: error.message
    });
  }
}

/**
 * 更新主播 - Express版本
 */
async function handleUpdateAnchor(anchorId, req, res) {
  try {
    if (!anchorId) {
      return res.status(400).json({
        error: "invalid_anchor_id",
        message: "无效的主播ID"
      });
    }

    const permissionCheck = checkAnchorPermission(req.authResult, 'modify');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    const { anchor_name, anchor_id, anchor_cookie, status, password, sort } = req.body;

    // 验证必填字段
    if (!anchor_name || !anchor_id || !password || !anchor_cookie) {
      return res.status(400).json({
        error: "missing_fields",
        message: "主播名称、主播ID、密码和主播Cookie为必填字段"
      });
    }

    // 验证密码
    const passwordValidation = await validatePassword(password, anchorId);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        error: "invalid_password",
        message: passwordValidation.message
      });
    }

    // 检查主播是否存在
    const checkResult = await database.all(
      "SELECT id FROM anchors WHERE id = ?",
      [anchorId]
    );

    if (checkResult.results.length === 0) {
      return res.status(404).json({
        error: "anchor_not_found",
        message: "主播不存在"
      });
    }

    // 检查主播ID是否被其他主播使用
    const duplicateResult = await database.all(
      "SELECT id FROM anchors WHERE anchor_id = ? AND id != ?",
      [anchor_id, anchorId]
    );

    if (duplicateResult.results.length > 0) {
      return res.status(400).json({
        error: "duplicate_anchor_id",
        message: "主播ID已被其他主播使用"
      });
    }

    // 更新主播信息（使用UTC+8时区）
    await database.run(`
      UPDATE anchors
      SET anchor_name = ?, anchor_id = ?, anchor_cookie = ?, status = ?, password = ?, sort = ?, updated_at = datetime('now', '+8 hours')
      WHERE id = ?
    `, [anchor_name, anchor_id, anchor_cookie, status || 'active', password, parseInt(sort) || 0, anchorId]);

    res.json({
      success: true,
      message: "主播更新成功"
    });
  } catch (error) {
    console.error("Error updating anchor:", error);
    res.status(500).json({
      error: "update_anchor_failed",
      message: "更新主播失败: " + error.message
    });
  }
}

/**
 * 删除主播 - Express版本
 */
async function handleDeleteAnchor(anchorId, req, res) {
  try {
    if (!anchorId) {
      return res.status(400).json({
        error: "invalid_anchor_id",
        message: "无效的主播ID"
      });
    }

    const permissionCheck = checkAnchorPermission(req.authResult, 'delete');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    // 检查主播是否存在
    const checkResult = await database.all(
      "SELECT id FROM anchors WHERE id = ?",
      [anchorId]
    );

    if (checkResult.results.length === 0) {
      return res.status(404).json({
        error: "anchor_not_found",
        message: "主播不存在"
      });
    }

    // 删除主播
    await database.run(
      "DELETE FROM anchors WHERE id = ?",
      [anchorId]
    );

    res.json({
      success: true,
      message: "主播删除成功"
    });
  } catch (error) {
    console.error("Error deleting anchor:", error);
    res.status(500).json({
      error: "delete_anchor_failed",
      message: "删除主播失败: " + error.message
    });
  }
}





/**
 * 检查密码是否重复 - Express版本
 */
async function handleCheckPassword(req, res) {
  try {
    const permissionCheck = checkAnchorPermission(req.authResult, 'check');
    if (!permissionCheck.hasPermission) {
      return res.status(403).json({
        error: "permission_denied",
        message: permissionCheck.message
      });
    }

    const { password, excludeId } = req.body;

    if (!password) {
      return res.status(400).json({
        error: "missing_password",
        message: "密码为必填参数"
      });
    }

    const validation = await validatePassword(password, excludeId);

    res.json({
      isDuplicate: !validation.isValid,
      isValid: validation.isValid,
      message: validation.message,
      usedBy: validation.isValid ? null : (password === config.auth.password ? "系统访问密码" : "其他主播")
    });
  } catch (error) {
    console.error("Error checking password:", error);
    res.status(500).json({
      error: "check_password_failed",
      message: "检查密码失败: " + error.message
    });
  }
}

















// 处理直播计划数据请求
async function handleLivePlansData(req, res) {
  try {
    const {
      anchor,
      liveDate,
      liveId,
      validStatus,
      liveStatus,
      page = CONSTANTS.PAGINATION.DEFAULT_PAGE,
      limit = CONSTANTS.PAGINATION.DEFAULT_LIMIT,
      sortField = 'live_date',
      sortOrder = 'DESC'
    } = req.query;

    // 构建WHERE条件
    let whereConditions = [];
    let params = [];

    // 主播筛选
    if (anchor && anchor.trim() !== '') {
      whereConditions.push('anchor_name = ?');
      params.push(anchor.trim());
    }

    // 直播日期筛选
    if (liveDate && liveDate.trim() !== '') {
      whereConditions.push('live_date = ?');
      params.push(liveDate.trim());
    }

    // 直播ID筛选
    if (liveId && liveId.trim() !== '') {
      whereConditions.push('live_id LIKE ?');
      params.push(`%${liveId.trim()}%`);
    }

    // 视频状态筛选
    if (validStatus && validStatus.trim() !== '') {
      whereConditions.push('valid_status = ?');
      params.push(validStatus.trim());
    }

    // 直播状态筛选
    if (liveStatus && liveStatus.trim() !== '') {
      whereConditions.push('live_status = ?');
      params.push(liveStatus.trim());
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 验证排序字段
    const validSortFields = ['live_id', 'anchor_name', 'live_date', 'product_count', 'total_sales', 'total_commission', 'created_at', 'average_amount', 'average_commission'];
    const actualSortField = validSortFields.includes(sortField) ? sortField : 'live_date';
    const actualSortOrder = ['ASC', 'DESC'].includes(sortOrder?.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    console.log(`排序参数: sortField=${sortField}, sortOrder=${sortOrder}, actualSortField=${actualSortField}, actualSortOrder=${actualSortOrder}`);

    // 构建查询SQL - 用户选择的排序为主要排序，直播状态为次要排序
    let sql = `SELECT * FROM live_plans ${whereClause}
               ORDER BY
                 ${actualSortField} ${actualSortOrder},
                 CASE
                   WHEN live_status = '直播中' THEN 0
                   WHEN live_status = '未开播' THEN 1
                   WHEN live_status = '已结束' THEN 2
                   WHEN live_status = '已取消' THEN 3
                   WHEN live_status = '未知' THEN 4
                   ELSE 5
                 END ASC
               LIMIT ? OFFSET ?`;
    let countSql = `SELECT COUNT(*) as total FROM live_plans ${whereClause}`;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 执行查询
    const [countResult, dataResult] = await Promise.all([
      database.all(countSql, params),
      database.all(sql, [...params, parseInt(limit), offset])
    ]);

    const total = countResult.results[0]?.total || 0;
    const livePlans = formatDataResults(dataResult.results);

    res.json({
      livePlans,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error("Error fetching live plans:", error);
    res.status(500).json({
      error: "Failed to fetch live plans",
      message: error.message
    });
  }
}

// 处理直播计划统计数据请求
async function handleLivePlansStats(req, res) {
  try {
    const {
      anchor,
      liveDate,
      liveId,
      validStatus,
      liveStatus
    } = req.query;

    // 构建WHERE条件
    let whereConditions = [];
    let params = [];

    // 主播筛选
    if (anchor && anchor.trim() !== '') {
      whereConditions.push('anchor_name = ?');
      params.push(anchor.trim());
    }

    // 直播日期筛选
    if (liveDate && liveDate.trim() !== '') {
      whereConditions.push('live_date = ?');
      params.push(liveDate.trim());
    }

    // 直播ID筛选
    if (liveId && liveId.trim() !== '') {
      whereConditions.push('live_id LIKE ?');
      params.push(`%${liveId.trim()}%`);
    }

    // 视频状态筛选
    if (validStatus && validStatus.trim() !== '') {
      whereConditions.push('valid_status = ?');
      params.push(validStatus.trim());
    }

    // 直播状态筛选
    if (liveStatus && liveStatus.trim() !== '') {
      whereConditions.push('live_status = ?');
      params.push(liveStatus.trim());
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取统计数据
    const statsQuery = `
      SELECT
        COUNT(*) as totalLives,
        COUNT(CASE WHEN live_status = '直播中' THEN 1 END) as liveLives,
        COUNT(CASE WHEN live_status = '未开播' THEN 1 END) as scheduledLives,
        COALESCE(SUM(total_sales), 0) as totalSales,
        COALESCE(SUM(total_commission), 0) as totalCommission
      FROM live_plans ${whereClause}
    `;

    const result = await database.all(statsQuery, params);
    const stats = result.results[0] || {};

    res.json({
      stats: {
        totalLives: stats.totalLives || 0,
        liveLives: stats.liveLives || 0,
        scheduledLives: stats.scheduledLives || 0,
        totalSales: parseFloat(stats.totalSales) || 0,
        totalCommission: parseFloat(stats.totalCommission) || 0
      }
    });

  } catch (error) {
    console.error("Error fetching live plans stats:", error);
    res.status(500).json({
      error: "Failed to fetch live plans stats",
      message: error.message
    });
  }
}

// 处理直播计划同步请求
async function handleLivePlansSync(req, res) {
  try {
    const { anchorName } = req.body;

    // 验证必需参数
    if (!validateRequiredParams({ anchorName }, ['anchorName'], res)) {
      return;
    }

    // 获取主播信息
    const anchorResult = await getAnchorByName(anchorName);
    if (!validateAnchorExists(anchorResult, res)) {
      return;
    }

    // 验证主播Cookie
    if (!validateAnchorCookie(anchorResult, res)) {
      return;
    }

    // 提取并验证h5Token
    const h5Token = extractAndValidateH5Token(anchorResult.anchor_cookie, res);
    if (!h5Token) {
      return;
    }



    // 创建同步实例
    const livePlanSync = new LivePlanSync();

    // 执行同步
    const syncResult = await livePlanSync.syncAnchorLivePlans(anchorName, h5Token, anchorResult.anchor_cookie);

    if (syncResult.success) {
      res.json({
        success: true,
        message: "同步完成",
        data: {
          anchorName: syncResult.anchorName,
          inserted: syncResult.inserted,
          updated: syncResult.updated,
          deleted: syncResult.deleted || 0,
          total: syncResult.total
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: syncResult.error,
        message: `同步失败: ${syncResult.error}`
      });
    }

  } catch (error) {
    console.error("Error syncing live plans:", error);
    res.status(500).json({
      error: "Failed to sync live plans",
      message: error.message
    });
  }
}

// 处理同步所有主播直播计划请求
async function handleLivePlansSyncAll(req, res) {
  try {
    // 获取所有活跃的主播
    const anchors = await getActiveAnchors();

    if (anchors.length === 0) {
      return sendErrorResponse(res, 404, "No active anchors found", "未找到活跃的主播或主播未配置Cookie");
    }
    const totalAnchors = anchors.length;
    let successCount = 0;
    let failedCount = 0;
    let totalInserted = 0;
    let totalUpdated = 0;
    let totalDeleted = 0;
    const failedAnchors = [];

    console.log(`开始同步 ${totalAnchors} 个主播的直播计划`);

    // 创建同步实例
    const livePlanSync = new LivePlanSync();

    // 逐个同步主播
    for (const anchor of anchors) {
      try {
        console.log(`正在同步主播: ${anchor.anchor_name}`);

        // 提取h5Token
        const h5Token = extractAndValidateH5Token(anchor.anchor_cookie, null);
        if (!h5Token) {
          console.error(`主播 ${anchor.anchor_name} 的Cookie中未找到有效的h5_tk token`);
          failedCount++;
          failedAnchors.push({
            name: anchor.anchor_name,
            error: '无法从Cookie中提取有效的h5_tk token'
          });
          continue;
        }

        // 执行同步
        const syncResult = await livePlanSync.syncAnchorLivePlans(anchor.anchor_name, h5Token, anchor.anchor_cookie);

        if (syncResult.success) {
          successCount++;
          totalInserted += syncResult.inserted || 0;
          totalUpdated += syncResult.updated || 0;
          totalDeleted += syncResult.deleted || 0;
          console.log(`主播 ${anchor.anchor_name} 同步成功: 新增${syncResult.inserted}, 更新${syncResult.updated}, 删除${syncResult.deleted}`);
        } else {
          failedCount++;
          failedAnchors.push({
            name: anchor.anchor_name,
            error: syncResult.error || '未知错误'
          });
          console.error(`主播 ${anchor.anchor_name} 同步失败:`, syncResult.error);
        }

        // 添加延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        failedCount++;
        failedAnchors.push({
          name: anchor.anchor_name,
          error: error.message || '未知错误'
        });
        console.error(`主播 ${anchor.anchor_name} 同步异常:`, error);
      }
    }

    console.log(`同步完成: 总计${totalAnchors}个主播, 成功${successCount}个, 失败${failedCount}个`);

    sendBatchOperationResponse(res, "批量同步完成", {
      totalCount: totalAnchors,
      successCount,
      failedCount,
      totalInserted,
      totalUpdated,
      totalDeleted
    }, failedAnchors);

  } catch (error) {
    handleApiError(res, error, "同步所有主播直播计划");
  }
}

// 处理直播产品同步请求
async function handleLiveProductsSync(req, res) {
  try {
    const { liveId } = req.body;

    // 验证必需参数
    if (!validateRequiredParams({ liveId }, ['liveId'], res)) {
      return;
    }

    // 根据直播ID获取主播信息
    const anchorResult = await getAnchorByLiveId(liveId);
    if (!anchorResult) {
      return sendErrorResponse(res, 404, "Live plan not found", "未找到指定的直播计划或对应的主播信息");
    }

    // 验证主播Cookie
    if (!validateAnchorCookie(anchorResult, res)) {
      return;
    }

    // 提取并验证h5Token
    const h5Token = extractAndValidateH5Token(anchorResult.anchor_cookie, res);
    if (!h5Token) {
      return;
    }

    // 创建同步实例
    const liveProductSync = new LiveProductSync();

    // 执行同步
    const syncResult = await liveProductSync.syncLiveProducts(liveId, anchorResult.anchor_name, h5Token, anchorResult.anchor_cookie);

    if (syncResult.success) {
      res.json({
        success: true,
        message: "产品提取完成",
        data: {
          liveId: syncResult.liveId,
          inserted: syncResult.inserted,
          updated: syncResult.updated,
          deleted: syncResult.deleted,
          total: syncResult.total,
          stats: syncResult.stats
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: syncResult.error,
        message: `产品提取失败: ${syncResult.error}`
      });
    }

  } catch (error) {
    console.error("Error syncing live products:", error);
    res.status(500).json({
      error: "Failed to sync live products",
      message: error.message
    });
  }
}

// 处理一键提取所有产品请求（提取三天内的直播计划产品）
async function handleLiveProductsExtractAll(req, res) {
  try {
    // 计算三天前的日期
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    const threeDaysAgoStr = threeDaysAgo.toISOString().split('T')[0];

    console.log(`开始一键提取产品，筛选条件：直播日期 >= ${threeDaysAgoStr}`);

    // 获取三天内的所有直播计划（根据直播日期筛选）
    const livePlansResult = await database.all(`
      SELECT DISTINCT lp.live_id, lp.anchor_name, a.anchor_cookie
      FROM live_plans lp
      JOIN anchors a ON lp.anchor_name = a.anchor_name
      WHERE lp.live_date >= ?
      AND a.status = 'active'
      AND a.anchor_cookie IS NOT NULL
      AND a.anchor_cookie != ''
      ORDER BY lp.live_date DESC
    `, [threeDaysAgoStr]);

    if (!livePlansResult.results || livePlansResult.results.length === 0) {
      return res.status(404).json({
        error: "No live plans found",
        message: "未找到三天内的直播计划或主播未配置Cookie"
      });
    }

    const livePlans = livePlansResult.results;
    const totalLives = livePlans.length;
    let successCount = 0;
    let failedCount = 0;
    let totalInserted = 0;
    let totalUpdated = 0;
    let totalDeleted = 0;
    const failedLives = [];

    console.log(`找到 ${totalLives} 个直播计划需要提取产品`);

    // 创建同步实例
    const liveProductSync = new LiveProductSync();

    // 逐个提取直播产品
    for (const livePlan of livePlans) {
      try {
        console.log(`正在提取直播 ${livePlan.live_id} (${livePlan.anchor_name}) 的产品...`);

        // 提取h5Token
        const h5Token = extractAndValidateH5Token(livePlan.anchor_cookie, null);
        if (!h5Token) {
          console.error(`主播 ${livePlan.anchor_name} 的Cookie中未找到有效的h5_tk token`);
          failedCount++;
          failedLives.push({
            liveId: livePlan.live_id,
            anchorName: livePlan.anchor_name,
            error: '无法从Cookie中提取有效的h5_tk token'
          });
          continue;
        }

        // 执行产品提取
        const syncResult = await liveProductSync.syncLiveProducts(
          livePlan.live_id,
          livePlan.anchor_name,
          h5Token,
          livePlan.anchor_cookie
        );

        if (syncResult.success) {
          successCount++;
          totalInserted += syncResult.inserted || 0;
          totalUpdated += syncResult.updated || 0;
          totalDeleted += syncResult.deleted || 0;
          console.log(`直播 ${livePlan.live_id} 产品提取成功: 新增${syncResult.inserted}, 更新${syncResult.updated}, 删除${syncResult.deleted}`);
        } else {
          failedCount++;
          failedLives.push({
            liveId: livePlan.live_id,
            anchorName: livePlan.anchor_name,
            error: syncResult.error || '未知错误'
          });
          console.error(`直播 ${livePlan.live_id} 产品提取失败:`, syncResult.error);
        }

        // 添加延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 800));

      } catch (error) {
        failedCount++;
        failedLives.push({
          liveId: livePlan.live_id,
          anchorName: livePlan.anchor_name,
          error: error.message || '未知错误'
        });
        console.error(`直播 ${livePlan.live_id} 产品提取异常:`, error);
      }
    }

    console.log(`一键提取产品完成: 总计${totalLives}个直播, 成功${successCount}个, 失败${failedCount}个`);

    sendBatchOperationResponse(res, "批量产品提取完成", {
      totalCount: totalLives,
      successCount,
      failedCount,
      totalInserted,
      totalUpdated,
      totalDeleted
    }, failedLives);

  } catch (error) {
    handleApiError(res, error, "一键提取所有产品");
  }
}

// 处理提取今日直播产品请求
async function handleLiveProductsExtractToday(req, res) {
  try {
    // 获取今日日期
    const today = new Date().toISOString().split('T')[0];

    console.log(`开始提取今日直播产品，筛选条件：直播日期 = ${today}`);

    // 获取今日的所有直播计划
    const livePlansResult = await database.all(`
      SELECT DISTINCT lp.live_id, lp.anchor_name, a.anchor_cookie
      FROM live_plans lp
      JOIN anchors a ON lp.anchor_name = a.anchor_name
      WHERE lp.live_date = ?
      AND a.status = 'active'
      AND a.anchor_cookie IS NOT NULL
      AND a.anchor_cookie != ''
      ORDER BY lp.live_date DESC
    `, [today]);

    await executeProductsExtraction(res, livePlansResult, "今日直播产品提取完成");

  } catch (error) {
    handleApiError(res, error, "提取今日直播产品");
  }
}

// 处理提取产品数量为0的直播产品请求
async function handleLiveProductsExtractZero(req, res) {
  try {
    console.log(`开始提取产品数量为0的直播产品`);

    // 获取产品数量为0的所有直播计划
    const livePlansResult = await database.all(`
      SELECT DISTINCT lp.live_id, lp.anchor_name, a.anchor_cookie
      FROM live_plans lp
      JOIN anchors a ON lp.anchor_name = a.anchor_name
      WHERE (lp.product_count IS NULL OR lp.product_count = 0)
      AND a.status = 'active'
      AND a.anchor_cookie IS NOT NULL
      AND a.anchor_cookie != ''
      ORDER BY lp.live_date DESC
    `);

    await executeProductsExtraction(res, livePlansResult, "产品数量为0的直播产品提取完成");

  } catch (error) {
    handleApiError(res, error, "提取产品数量为0的直播产品");
  }
}

// 处理提取3天内直播产品请求
async function handleLiveProductsExtractRecent(req, res) {
  try {
    // 计算三天前的日期
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    const threeDaysAgoStr = threeDaysAgo.toISOString().split('T')[0];

    console.log(`开始提取3天内直播产品，筛选条件：直播日期 >= ${threeDaysAgoStr}`);

    // 获取三天内的所有直播计划
    const livePlansResult = await database.all(`
      SELECT DISTINCT lp.live_id, lp.anchor_name, a.anchor_cookie
      FROM live_plans lp
      JOIN anchors a ON lp.anchor_name = a.anchor_name
      WHERE lp.live_date >= ?
      AND a.status = 'active'
      AND a.anchor_cookie IS NOT NULL
      AND a.anchor_cookie != ''
      ORDER BY lp.live_date DESC
    `, [threeDaysAgoStr]);

    await executeProductsExtraction(res, livePlansResult, "3天内直播产品提取完成");

  } catch (error) {
    handleApiError(res, error, "提取3天内直播产品");
  }
}

// 公共的产品提取执行函数
async function executeProductsExtraction(res, livePlansResult, successMessage) {
  if (!livePlansResult.results || livePlansResult.results.length === 0) {
    return res.status(404).json({
      error: "No live plans found",
      message: "未找到符合条件的直播计划或主播未配置Cookie"
    });
  }

  const livePlans = livePlansResult.results;
  const totalLives = livePlans.length;
  let successCount = 0;
  let failedCount = 0;
  let totalUpdated = 0;
  const failedLives = [];

  console.log(`找到 ${totalLives} 个直播计划需要提取产品`);

  // 创建同步实例
  const liveProductSync = new LiveProductSync();

  // 逐个提取直播产品
  for (const livePlan of livePlans) {
    try {
      console.log(`正在提取直播 ${livePlan.live_id} (${livePlan.anchor_name}) 的产品...`);

      // 提取h5Token
      const h5Token = extractAndValidateH5Token(livePlan.anchor_cookie, null);
      if (!h5Token) {
        console.error(`主播 ${livePlan.anchor_name} 的Cookie中未找到有效的h5_tk token`);
        failedCount++;
        failedLives.push({
          liveId: livePlan.live_id,
          anchorName: livePlan.anchor_name,
          error: '无法从Cookie中提取有效的h5_tk token'
        });
        continue;
      }

      // 执行产品提取
      const syncResult = await liveProductSync.syncLiveProducts(
        livePlan.live_id,
        livePlan.anchor_name,
        h5Token,
        livePlan.anchor_cookie
      );

      if (syncResult.success) {
        successCount++;
        // 如果有产品更新或新增，则计入成功
        if ((syncResult.inserted || 0) > 0 || (syncResult.updated || 0) > 0) {
          totalUpdated++;
        }
        console.log(`直播 ${livePlan.live_id} 产品提取成功: 新增${syncResult.inserted}, 更新${syncResult.updated}`);
      } else {
        failedCount++;
        failedLives.push({
          liveId: livePlan.live_id,
          anchorName: livePlan.anchor_name,
          error: syncResult.error || '未知错误'
        });
        console.error(`直播 ${livePlan.live_id} 产品提取失败:`, syncResult.error);
      }

      // 添加延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 800));

    } catch (error) {
      failedCount++;
      failedLives.push({
        liveId: livePlan.live_id,
        anchorName: livePlan.anchor_name,
        error: error.message || '未知错误'
      });
      console.error(`直播 ${livePlan.live_id} 产品提取异常:`, error);
    }
  }

  console.log(`产品提取完成: 总计${totalLives}个直播, 成功${successCount}个, 失败${failedCount}个`);

  res.json({
    success: true,
    message: successMessage,
    data: {
      processed: totalLives,
      updated: totalUpdated,
      errors: failedCount
    }
  });
}

// 处理直播产品列表请求
async function handleLiveProductsList(req, res) {
  try {
    const { liveId } = req.query;

    if (!liveId) {
      return res.status(400).json({
        error: "Missing required parameters",
        message: "直播ID是必需的"
      });
    }

    // 查询该直播的所有产品
    const productsResult = await database.all(
      `SELECT * FROM live_products WHERE live_id = ? ORDER BY product_sequence ASC`,
      [liveId]
    );

    const products = formatDataResults(productsResult.results || []);

    res.json({
      success: true,
      products: products,
      total: products.length
    });

  } catch (error) {
    console.error("Error fetching live products:", error);
    res.status(500).json({
      error: "Failed to fetch live products",
      message: error.message
    });
  }
}

// 处理直播产品统计请求
async function handleLiveProductsStats(req, res) {
  try {
    const { liveId } = req.query;

    if (!liveId) {
      return res.status(400).json({
        error: "Missing required parameters",
        message: "直播ID是必需的"
      });
    }

    // 查询基础统计信息
    const basicStatsResult = await database.get(`
      SELECT
        COUNT(*) as product_count,
        AVG(amount) as avg_price,
        AVG(commission_amount) as avg_commission,
        SUM(CASE WHEN explanation_status = '讲解通过' THEN 1 ELSE 0 END) as passed_count,
        SUM(CASE WHEN explanation_status = '讲解未通过' THEN 1 ELSE 0 END) as failed_count,
        SUM(CASE WHEN explanation_status IS NOT NULL AND explanation_status != '' AND explanation_status != '未讲解' THEN 1 ELSE 0 END) as explained_count,
        SUM(CASE WHEN script_content IS NOT NULL AND script_content != '' THEN 1 ELSE 0 END) as hand_card_count,
        SUM(CASE WHEN audio_extraction_status = '已生成' THEN 1 ELSE 0 END) as audio_count,
        SUM(CASE WHEN push_status = '已推送' THEN 1 ELSE 0 END) as push_count
      FROM live_products
      WHERE live_id = ?
    `, [liveId]);

    const stats = basicStatsResult || {};

    res.json({
      success: true,
      stats: {
        productCount: stats.product_count || 0,
        avgPrice: parseFloat(stats.avg_price) || 0,
        avgCommission: parseFloat(stats.avg_commission) || 0,
        passedCount: stats.passed_count || 0,
        failedCount: stats.failed_count || 0,
        explainedCount: stats.explained_count || 0,
        handCardCount: stats.hand_card_count || 0,
        audioCount: stats.audio_count || 0,
        pushCount: stats.push_count || 0
      }
    });

  } catch (error) {
    console.error("Error fetching live products stats:", error);
    res.status(500).json({
      error: "Failed to fetch live products stats",
      message: error.message
    });
  }
}

// 处理清空直播产品请求
async function handleLiveProductsClear(req, res) {
  try {
    const { liveId } = req.body;

    if (!liveId) {
      return res.status(400).json({
        error: "Missing required parameters",
        message: "直播ID是必需的"
      });
    }

    // 删除该直播的所有产品
    const deleteResult = await database.run(
      `DELETE FROM live_products WHERE live_id = ?`,
      [liveId]
    );

    // 更新直播计划的统计信息
    await database.run(`
      UPDATE live_plans SET
        product_count = 0,
        average_amount = 0,
        average_commission = 0,
        updated_at = datetime('now', '+8 hours')
      WHERE live_id = ?
    `, [liveId]);

    res.json({
      success: true,
      message: "产品已清空",
      deleted: deleteResult.changes || 0
    });

  } catch (error) {
    console.error("Error clearing live products:", error);
    res.status(500).json({
      error: "Failed to clear live products",
      message: error.message
    });
  }
}

/**
 * 处理创建直播计划请求
 */
async function handleLivePlansCreate(req, res) {
  try {
    const { anchorName, appointmentTime, liveEndTime } = req.body;

    // 验证必填参数
    if (!anchorName || !appointmentTime || !liveEndTime) {
      return res.status(400).json({
        success: false,
        error: 'missing_parameters',
        message: '缺少必填参数：anchorName, appointmentTime, liveEndTime'
      });
    }

    // 验证时间参数
    if (typeof appointmentTime !== 'number' || typeof liveEndTime !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'invalid_time_format',
        message: '时间参数必须是数字类型的时间戳'
      });
    }

    if (appointmentTime >= liveEndTime) {
      return res.status(400).json({
        success: false,
        error: 'invalid_time_range',
        message: '开始时间必须早于结束时间'
      });
    }

    console.log(`🎯 开始为主播 ${anchorName} 创建直播计划`);
    console.log(`⏰ 开始时间: ${new Date(appointmentTime).toLocaleString('zh-CN')}`);
    console.log(`⏰ 结束时间: ${new Date(liveEndTime).toLocaleString('zh-CN')}`);

    // 动态导入LivePlanCreator
    const { LivePlanCreator } = await import('./live-plan-creator.js');
    const creator = new LivePlanCreator();

    // 创建直播计划
    const result = await creator.createLivePlanForAnchor(anchorName, appointmentTime, liveEndTime);

    if (result.success) {
      console.log(`✅ 主播 ${anchorName} 直播计划创建成功，liveId: ${result.liveId}`);

      res.json({
        success: true,
        message: '直播计划创建成功',
        data: {
          liveId: result.liveId,
          anchorName: result.anchorName
        }
      });
    } else {
      console.error(`❌ 主播 ${anchorName} 直播计划创建失败: ${result.error}`);

      res.status(400).json({
        success: false,
        error: 'create_failed',
        message: result.error || '创建直播计划失败'
      });
    }

  } catch (error) {
    console.error('创建直播计划失败:', error);
    console.error('错误堆栈:', error.stack);

    res.status(500).json({
      success: false,
      error: 'internal_error',
      message: '服务器内部错误: ' + error.message
    });
  }
}

/**
 * 批量更新所有主播Cookie - Express版本
 */
async function handleUpdateCookiesBatch(req, res) {
  try {
    console.log('=== 批量Cookie更新接口开始 ===');

    // 检查权限 - 只有管理员可以执行批量更新
    if (req.authResult.userType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'permission_denied',
        message: '只有管理员可以执行批量Cookie更新'
      });
    }

    console.log('开始执行批量Cookie更新任务...');
    
    // 调用Cookie更新任务
    const result = await triggerCookieUpdateJob();
    
    console.log('批量Cookie更新任务完成');
    console.log('=== 批量Cookie更新接口结束 - 成功 ===');

    res.json({
      success: result.success,
      message: result.message,
      data: result.data
    });

  } catch (error) {
    console.error('批量Cookie更新失败:', error);
    console.error('错误堆栈:', error.stack);
    console.log('=== 批量Cookie更新接口结束 - 失败 ===');

    res.status(500).json({
      success: false,
      error: 'batch_update_failed',
      message: '批量Cookie更新失败: ' + error.message,
      data: {
        totalAnchors: 0,
        successCount: 0,
        failedCount: 0,
        noUpdateCount: 0,
        failedAnchors: []
      }
    });
  }
}

/**
 * 处理一键隐藏场次请求
 */
async function handleLivePlansHideAll(req, res) {
  try {
    console.log('🚀 开始处理一键隐藏场次请求');

    const hideManager = new LiveHideManager();
    const result = await hideManager.hideAllDisplayedLives();

    if (result.success) {
      console.log(`✅ 隐藏场次完成 - 成功: ${result.hiddenCount}, 失败: ${result.errors.length}`);

      res.json({
        success: true,
        message: `成功隐藏 ${result.hiddenCount} 个直播场次`,
        hiddenCount: result.hiddenCount,
        errors: result.errors
      });
    } else {
      console.error('❌ 隐藏场次失败:', result.errors);
      res.status(500).json({
        success: false,
        error: '隐藏场次失败',
        message: result.errors.join('; ')
      });
    }

  } catch (error) {
    console.error('💥 处理隐藏场次请求异常:', error);
    res.status(500).json({
      success: false,
      error: '服务器错误',
      message: error.message
    });
  }
}

/**
 * 处理展示隐藏选中主播场次请求
 */
async function handleLivePlansHideSelected(req, res) {
  try {
    console.log('📥 接收到的请求体:', req.body);
    const { anchorNames, hide } = req.body;

    // 默认为隐藏操作（向后兼容）
    const hideValue = hide !== undefined ? hide : 1;
    const actionText = hideValue === 1 ? '隐藏' : '展示';

    console.log(`🚀 开始处理${actionText}选中主播场次请求`);
    console.log('📋 提取的anchorNames:', anchorNames);
    console.log('📋 hide参数:', hideValue);
    console.log('📋 anchorNames类型:', typeof anchorNames);
    console.log('📋 是否为数组:', Array.isArray(anchorNames));
    console.log('📋 数组长度:', anchorNames ? anchorNames.length : 'undefined');

    // 验证参数
    if (!anchorNames || !Array.isArray(anchorNames) || anchorNames.length === 0) {
      console.log('❌ 参数验证失败');
      return res.status(400).json({
        success: false,
        error: '参数错误',
        message: `请提供要${actionText}的主播名称列表`
      });
    }

    const hideManager = new LiveHideManager();
    const result = await hideManager.setSelectedAnchorsLivesStatus(anchorNames, hideValue);

    if (result.success) {
      const count = hideValue === 1 ? result.hiddenCount : result.showCount;
      console.log(`✅ ${actionText}选中主播场次完成 - 成功: ${count}, 失败: ${result.errors.length}`);

      res.json({
        success: true,
        message: `成功${actionText} ${count} 个直播场次`,
        hiddenCount: result.hiddenCount,
        showCount: result.showCount,
        errors: result.errors
      });
    } else {
      console.error(`❌ ${actionText}选中主播场次失败:`, result.errors);
      res.status(500).json({
        success: false,
        error: `${actionText}场次失败`,
        message: result.errors.join('; ')
      });
    }

  } catch (error) {
    console.error(`💥 处理${actionText}选中主播场次请求异常:`, error);
    res.status(500).json({
      success: false,
      error: '服务器错误',
      message: error.message
    });
  }
}

/**
 * 处理批量上品请求
 */
async function handleBatchAddProducts(req, res) {
  try {
    const { liveId, anchorName, productIds } = req.body;

    if (!liveId || !anchorName || !productIds || !Array.isArray(productIds)) {
      return res.status(400).json({
        success: false,
        error: "Missing required parameters",
        message: "直播ID、主播名称和产品ID列表是必需的"
      });
    }

    console.log(`🚀 开始批量上品 - 直播ID: ${liveId}, 主播: ${anchorName}, 产品数量: ${productIds.length}`);

    // 根据主播名称获取主播信息
    const anchorResult = await getAnchorByName(anchorName);
    if (!anchorResult) {
      return sendErrorResponse(res, 404, "Anchor not found", "未找到指定的主播信息");
    }

    // 验证主播Cookie
    if (!validateAnchorCookie(anchorResult, res)) {
      return;
    }

    // 提取并验证h5Token
    const h5Token = extractAndValidateH5Token(anchorResult.anchor_cookie, res);
    if (!h5Token) {
      return;
    }

    // 创建批量上品实例
    const batchAddProducts = new BatchAddProducts();

    // 执行批量添加
    const result = await batchAddProducts.batchAddProductsWithPaging(
      liveId,
      productIds,
      anchorName,
      h5Token,
      anchorResult.anchor_cookie
    );

    if (result.success) {
      // 如果cookie有更新，保存新的cookie
      if (result.cookieUpdated && result.updatedCookie) {
        await updateAnchorCookie(anchorResult.id, result.updatedCookie);
      }

      res.json({
        success: true,
        message: "批量上品完成",
        totalCount: result.totalCount,
        successCount: result.successCount,
        failedCount: result.failedCount,
        successItems: result.successItems,
        failedItems: result.failedItems,
        failedBatches: result.failedBatches
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        message: `批量上品失败: ${result.error}`,
        totalCount: result.totalCount,
        successCount: result.successCount,
        failedCount: result.failedCount
      });
    }

  } catch (error) {
    console.error("Error in batch add products:", error);
    res.status(500).json({
      success: false,
      error: "Failed to batch add products",
      message: error.message
    });
  }
}

// 初始化任务管理器（启动任务处理）
taskManager.processNextTask();

// 导出路由器作为默认导出
export default router;

